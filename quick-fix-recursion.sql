-- QUICK FIX for infinite recursion in profiles table
-- Run this immediately to restore database access

-- 1. Drop the problematic policies causing recursion
DROP POLICY IF EXISTS "Users can update own profile or admins can update all" ON public.profiles;
DROP POLICY IF EXISTS "Users can view own profile or admins can view all" ON public.profiles;

-- 2. Disable <PERSON><PERSON> temporarily on profiles table to restore access
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 3. Create simple, non-recursive policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT TO authenticated
    USING (id = auth.uid());

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE TO authenticated
    USING (id = auth.uid());

-- 4. Verify the fix
SELECT 'Profiles table fixed - recursion resolved' as status;
