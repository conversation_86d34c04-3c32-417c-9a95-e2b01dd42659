import React, { useState } from "react";
import { QuickAnswer } from "../types/assessment";

interface QuickAnswerButtonsProps {
  questionId: string;
  quickAnswers: QuickAnswer[];
  onAnswerSelect: (
    questionId: string,
    quickAnswerId: string,
    answerText: string,
  ) => void;
  disabled?: boolean;
  isConfirmation?: boolean; // Special styling for Yes/No confirmation buttons
}

export const QuickAnswerButtons: React.FC<QuickAnswerButtonsProps> = ({
  questionId,
  quickAnswers,
  onAnswerSelect,
  disabled = false,
  isConfirmation = false,
}) => {
  const [selectedAnswerId, setSelectedAnswerId] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleAnswerClick = async (answer: QuickAnswer) => {
    console.log("🎯 [QuickAnswerButtons] Button clicked:", {
      answerId: answer.id,
      answerText: answer.answer_text,
    });

    if (disabled || isProcessing || selectedAnswerId) {
      console.log("⚠️ [QuickAnswerButtons] Click ignored:", {
        disabled,
        isProcessing,
        selectedAnswerId,
      });
      return;
    }

    console.log("🔄 [QuickAnswerButtons] Processing answer...");
    setSelectedAnswerId(answer.id);
    setIsProcessing(true);

    try {
      console.log("🚀 [QuickAnswerButtons] Calling onAnswerSelect...");
      await onAnswerSelect(questionId, answer.id, answer.answer_text);
      console.log("✅ [QuickAnswerButtons] onAnswerSelect completed");
    } catch (error) {
      console.error("❌ [QuickAnswerButtons] Error selecting answer:", error);
      setSelectedAnswerId(null);
    } finally {
      console.log("🔄 [QuickAnswerButtons] Setting isProcessing to false");
      setIsProcessing(false);
    }
  };

  // Don't render if already answered
  if (selectedAnswerId && !isProcessing) {
    return null;
  }

  return (
    <div className="flex flex-col gap-2 mt-3 mb-4" data-oid="f70rjlw">
      {!isConfirmation && (
        <div
          className="text-xs text-gray-500 dark:text-gray-400 mb-1"
          data-oid="2ciyurv"
        >
          Quick responses:
        </div>
      )}

      <div
        className={
          isConfirmation
            ? "flex flex-row gap-3 justify-center"
            : "flex flex-col gap-2"
        }
        data-oid="-8seo9-"
      >
        {quickAnswers
          .sort((a, b) => a.order - b.order)
          .map((answer) => {
            const isSelected = selectedAnswerId === answer.id;
            const isDisabled =
              disabled || isProcessing || (selectedAnswerId && !isSelected);

            // Special styling for confirmation buttons
            const getConfirmationStyle = () => {
              if (!isConfirmation) return "";

              const isYes =
                answer.id === "yes" ||
                answer.answer_text.toLowerCase() === "yes";
              const baseStyle =
                "px-6 py-3 text-sm font-medium rounded-lg border-2 transition-all duration-200 min-w-[100px]";

              if (isSelected) {
                return isYes
                  ? `${baseStyle} border-green-500 bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100`
                  : `${baseStyle} border-red-500 bg-red-50 dark:bg-red-900/20 text-red-900 dark:text-red-100`;
              }

              if (isDisabled) {
                return `${baseStyle} border-gray-200 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed`;
              }

              return isYes
                ? `${baseStyle} border-green-200 dark:border-green-600 bg-white dark:bg-gray-800 text-green-700 dark:text-green-300 hover:border-green-400 dark:hover:border-green-500 hover:bg-green-50 dark:hover:bg-green-900/10 cursor-pointer`
                : `${baseStyle} border-red-200 dark:border-red-600 bg-white dark:bg-gray-800 text-red-700 dark:text-red-300 hover:border-red-400 dark:hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/10 cursor-pointer`;
            };

            const regularStyle = `
              px-4 py-3 text-sm text-left rounded-lg border-2 transition-all duration-200
              ${
                isSelected
                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100"
                  : isDisabled
                    ? "border-gray-200 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed"
                    : "border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/10 cursor-pointer"
              }
              ${isProcessing ? "opacity-75" : ""}
            `;

            return (
              <button
                key={answer.id}
                onClick={() => handleAnswerClick(answer)}
                disabled={isDisabled}
                className={
                  isConfirmation ? getConfirmationStyle() : regularStyle
                }
                data-oid="ky2r:2:"
              >
                <div
                  className="flex items-center justify-between"
                  data-oid="g7fiq:q"
                >
                  <span className="flex-1 pr-2" data-oid="o9fo-t4">
                    {answer.answer_text}
                  </span>

                  {isSelected && (
                    <div className="flex items-center" data-oid="kotf4dq">
                      {isProcessing ? (
                        <svg
                          className="w-4 h-4 text-blue-500 animate-spin"
                          fill="none"
                          viewBox="0 0 24 24"
                          data-oid="qiyqui-"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                            data-oid="x15eiu2"
                          />

                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            data-oid="8nev9br"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4 text-blue-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          data-oid=".bmliik"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                            data-oid="7e-17q8"
                          />
                        </svg>
                      )}
                    </div>
                  )}
                </div>
              </button>
            );
          })}
      </div>

      {isProcessing && (
        <div
          className="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center"
          data-oid="lclgrvs"
        >
          <svg
            className="w-3 h-3 mr-1 animate-spin"
            fill="none"
            viewBox="0 0 24 24"
            data-oid="adr.8m."
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
              data-oid="pq44a3o"
            />

            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              data-oid="obgr0ra"
            />
          </svg>
          Processing your response...
        </div>
      )}
    </div>
  );
};
