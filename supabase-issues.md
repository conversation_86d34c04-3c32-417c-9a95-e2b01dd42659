| name | title | level | facing | categories | description | detail | remediation | metadata | cache_key |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| policy_exists_rls_disabled | Policy Exists RLS Disabled | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) policies have been created, but RLS has not been enabled for the underlying table. | Table \`public.profiles\\` has RLS policies but RLS is not enabled on the table. Policies include {"Users can update own profile or admins can update all","Users can view own profile or admins can view all"}. | https://supabase.com/docs/guides/database/database-linter?lint=0007_policy_exists_rls_disabled | {"name":"profiles","type":"table","schema":"public"} | policy_exists_rls_disabled_public_profiles |
| security_definer_view | Security Definer View | ERROR | EXTERNAL | ["SECURITY"] | Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user | View \`public.chat_sessions_with_stats\\` is defined with the SECURITY DEFINER property | https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view | {"name":"chat_sessions_with_stats","type":"view","schema":"public"} | security_definer_view_public_chat_sessions_with_stats |
| rls_disabled_in_public | RLS Disabled in Public | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST | Table \`public.assessments\\` is public, but RLS has not been enabled. | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public | {"name":"assessments","type":"table","schema":"public"} | rls_disabled_in_public_public_assessments |
| rls_disabled_in_public | RLS Disabled in Public | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST | Table \`public.assessment_questions\\` is public, but RLS has not been enabled. | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public | {"name":"assessment_questions","type":"table","schema":"public"} | rls_disabled_in_public_public_assessment_questions |
| rls_disabled_in_public | RLS Disabled in Public | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST | Table \`public.profiles\\` is public, but RLS has not been enabled. | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public | {"name":"profiles","type":"table","schema":"public"} | rls_disabled_in_public_public_profiles |
| rls_disabled_in_public | RLS Disabled in Public | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST | Table \`public.assessment_quick_answers\\` is public, but RLS has not been enabled. | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public | {"name":"assessment_quick_answers","type":"table","schema":"public"} | rls_disabled_in_public_public_assessment_quick_answers |
| rls_disabled_in_public | RLS Disabled in Public | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST | Table \`public.assessment_results\\` is public, but RLS has not been enabled. | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public | {"name":"assessment_results","type":"table","schema":"public"} | rls_disabled_in_public_public_assessment_results |
| rls_disabled_in_public | RLS Disabled in Public | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST | Table \`public.teams\\` is public, but RLS has not been enabled. | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public | {"name":"teams","type":"table","schema":"public"} | rls_disabled_in_public_public_teams |
| rls_disabled_in_public | RLS Disabled in Public | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST | Table \`public.team_members\\` is public, but RLS has not been enabled. | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public | {"name":"team_members","type":"table","schema":"public"} | rls_disabled_in_public_public_team_members |