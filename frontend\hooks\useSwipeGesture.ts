import { useRef, useCallback, TouchEvent, MouseEvent } from 'react';

export interface SwipeGestureConfig {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeStart?: (startX: number, startY: number) => void;
  onSwipeMove?: (deltaX: number, deltaY: number, currentX: number, currentY: number) => void;
  onSwipeEnd?: (deltaX: number, deltaY: number, velocity: number) => void;
  threshold?: number; // Minimum distance for a swipe to be registered
  velocityThreshold?: number; // Minimum velocity for a swipe
  preventDefaultTouch?: boolean; // Prevent default touch behavior
}

export interface SwipeState {
  isActive: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  deltaX: number;
  deltaY: number;
  startTime: number;
}

export const useSwipeGesture = (config: SwipeGestureConfig = {}) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeStart,
    onSwipeMove,
    onSwipeEnd,
    threshold = 50, // 50px minimum swipe distance
    velocityThreshold = 0.3, // pixels per millisecond
    preventDefaultTouch = true,
  } = config;

  const swipeState = useRef<SwipeState>({
    isActive: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    deltaX: 0,
    deltaY: 0,
    startTime: 0,
  });

  const getEventCoordinates = (event: TouchEvent | MouseEvent) => {
    if ('touches' in event) {
      // Touch event
      const touch = event.touches[0] || event.changedTouches[0];
      return { x: touch.clientX, y: touch.clientY };
    } else {
      // Mouse event
      return { x: event.clientX, y: event.clientY };
    }
  };

  const handleStart = useCallback((event: TouchEvent | MouseEvent) => {
    const { x, y } = getEventCoordinates(event);
    
    swipeState.current = {
      isActive: true,
      startX: x,
      startY: y,
      currentX: x,
      currentY: y,
      deltaX: 0,
      deltaY: 0,
      startTime: Date.now(),
    };

    onSwipeStart?.(x, y);

    if (preventDefaultTouch && 'touches' in event) {
      event.preventDefault();
    }
  }, [onSwipeStart, preventDefaultTouch]);

  const handleMove = useCallback((event: TouchEvent | MouseEvent) => {
    if (!swipeState.current.isActive) return;

    const { x, y } = getEventCoordinates(event);
    const deltaX = x - swipeState.current.startX;
    const deltaY = y - swipeState.current.startY;

    swipeState.current.currentX = x;
    swipeState.current.currentY = y;
    swipeState.current.deltaX = deltaX;
    swipeState.current.deltaY = deltaY;

    onSwipeMove?.(deltaX, deltaY, x, y);

    if (preventDefaultTouch && 'touches' in event) {
      event.preventDefault();
    }
  }, [onSwipeMove, preventDefaultTouch]);

  const handleEnd = useCallback((event: TouchEvent | MouseEvent) => {
    if (!swipeState.current.isActive) return;

    const { deltaX, deltaY, startTime } = swipeState.current;
    const endTime = Date.now();
    const duration = endTime - startTime;
    const velocity = Math.abs(deltaX) / duration; // pixels per millisecond

    // Call onSwipeEnd callback
    onSwipeEnd?.(deltaX, deltaY, velocity);

    // Determine if this is a valid swipe
    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);
    
    // Check if horizontal swipe is dominant and meets thresholds
    if (absX > absY && absX > threshold && velocity > velocityThreshold) {
      if (deltaX > 0) {
        onSwipeRight?.();
      } else {
        onSwipeLeft?.();
      }
    }

    // Reset state
    swipeState.current.isActive = false;

    if (preventDefaultTouch && 'touches' in event) {
      event.preventDefault();
    }
  }, [onSwipeLeft, onSwipeRight, onSwipeEnd, threshold, velocityThreshold, preventDefaultTouch]);

  // Touch event handlers
  const onTouchStart = useCallback((event: TouchEvent) => {
    handleStart(event);
  }, [handleStart]);

  const onTouchMove = useCallback((event: TouchEvent) => {
    handleMove(event);
  }, [handleMove]);

  const onTouchEnd = useCallback((event: TouchEvent) => {
    handleEnd(event);
  }, [handleEnd]);

  // Mouse event handlers (for desktop testing)
  const onMouseDown = useCallback((event: MouseEvent) => {
    handleStart(event);
  }, [handleStart]);

  const onMouseMove = useCallback((event: MouseEvent) => {
    handleMove(event);
  }, [handleMove]);

  const onMouseUp = useCallback((event: MouseEvent) => {
    handleEnd(event);
  }, [handleEnd]);

  return {
    // Touch handlers
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    // Mouse handlers (for desktop testing)
    onMouseDown,
    onMouseMove,
    onMouseUp,
    // Current swipe state
    swipeState: swipeState.current,
  };
};
