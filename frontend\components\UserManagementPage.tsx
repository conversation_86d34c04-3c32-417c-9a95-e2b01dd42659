import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { PlusIcon, UsersIcon } from "../constants";
import UserDeleteModal from "./UserDeleteModal";
import UserTable from "./UserTable";
import UserAddModal from "./UserAddModal";
import UserEditModal from "./UserEditModal";
import { dataService } from "../services/dataService";

// Define View type locally
type View = "chat" | "settings" | "userManagement";

interface UserData {
  id: string;
  name: string;
  email: string;
  role: "admin" | "user";
  avatarUrl?: string;
  createdAt: Date;
  lastActive: Date;
}

interface UserManagementPageProps {
  navigateTo: (view: View) => void;
}

const UserManagementPage: React.FC<UserManagementPageProps> = ({
  navigateTo,
}) => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);

  // Check admin status and load users
  useEffect(() => {
    checkAdminAndLoadUsers();
  }, []);

  const checkAdminAndLoadUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is admin
      const adminStatus = await dataService.isUserAdmin();
      setIsAdmin(adminStatus);

      if (!adminStatus) {
        setError(
          t("accessDenied", "Access denied. Admin privileges required."),
        );
        return;
      }

      // Load users if admin
      const usersData = await dataService.getAllUsers();
      setUsers(usersData);
    } catch (err) {
      console.error("Error loading users:", err);
      setError(t("errorLoadingUsers", "Error loading users"));
    } finally {
      setLoading(false);
    }
  };

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const usersData = await dataService.getAllUsers();
      setUsers(usersData);
    } catch (err) {
      console.error("Error loading users:", err);
      setError(t("errorLoadingUsers", "Error loading users"));
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const handleAddUser = () => {
    setShowAddModal(true);
  };

  const handleEditUser = (user: UserData) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  const handleDeleteUser = (user: UserData) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const confirmAdd = async (userData: {
    name: string;
    email: string;
    password: string;
    role: "admin" | "user";
  }) => {
    try {
      const newUser = await dataService.createUser(
        userData.name,
        userData.email,
        userData.password,
        userData.role,
      );
      if (newUser) {
        setUsers((prevUsers) => [newUser, ...prevUsers]);
        setShowAddModal(false);
      }
    } catch (err) {
      throw new Error(t("errorAddingUser", "Failed to add user"));
    }
  };

  const confirmEdit = async (
    userId: string,
    userData: { name: string; email: string; role: "admin" | "user" },
  ) => {
    try {
      const updatedUser = await dataService.updateUser(userId, userData);
      if (updatedUser) {
        setUsers((prevUsers) =>
          prevUsers.map((user) => (user.id === userId ? updatedUser : user)),
        );
        setShowEditModal(false);
        setSelectedUser(null);
      }
    } catch (err) {
      throw new Error(t("errorUpdatingUser", "Failed to update user"));
    }
  };

  const confirmDelete = async () => {
    if (selectedUser) {
      try {
        const success = await dataService.deleteUser(selectedUser.id);
        if (success) {
          setUsers(users.filter((u) => u.id !== selectedUser.id));
          setShowDeleteModal(false);
          setSelectedUser(null);
        }
      } catch (err) {
        console.error("Error deleting user:", err);
        setError(t("errorDeletingUser", "Failed to delete user"));
      }
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setSelectedUser(null);
  };

  const cancelAdd = () => {
    setShowAddModal(false);
  };

  const cancelEdit = () => {
    setShowEditModal(false);
    setSelectedUser(null);
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-white dark:bg-slate-900">
        <div className="text-slate-600 dark:text-slate-400">
          {t("loading", "Loading...")}
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-white dark:bg-slate-900">
      {/* Header */}
      <div className="border-b border-slate-200 dark:border-slate-700 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <UsersIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />

            <div>
              <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                {t("userManagement")}
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {t(
                  "manageUsersDescription",
                  "Manage user accounts and permissions",
                )}
              </p>
            </div>
          </div>
          <button
            onClick={handleAddUser}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <PlusIcon className="w-4 h-4" />
            <span>{t("addUser")}</span>
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="p-6 border-b border-slate-200 dark:border-slate-700">
        <input
          type="text"
          placeholder={t("searchUsers", "Search users...")}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full max-w-md px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-6 mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Users Table */}
      <UserTable
        users={filteredUsers}
        onDelete={handleDeleteUser}
        onEdit={handleEditUser}
      />

      {/* Add User Modal */}
      <UserAddModal
        isOpen={showAddModal}
        onClose={cancelAdd}
        onAdd={confirmAdd}
      />

      {/* Edit User Modal */}
      <UserEditModal
        isOpen={showEditModal}
        onClose={cancelEdit}
        onEdit={confirmEdit}
        user={selectedUser}
      />

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedUser && (
        <UserDeleteModal
          user={selectedUser}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}
    </div>
  );
};

export default UserManagementPage;
