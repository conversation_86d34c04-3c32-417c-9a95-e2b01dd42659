-- Fix Supabase Security Issues
-- This script addresses all RLS and security definer issues identified by Supa<PERSON> linter

-- =============================================================================
-- 1. FIRST, DROP EXISTING PROBLEMATIC POLICIES ON PROFILES TABLE
-- =============================================================================

-- Drop existing policies that might cause recursion or conflicts
DROP POLICY IF EXISTS "Users can update own profile or admins can update all" ON public.profiles;
DROP POLICY IF EXISTS "Users can view own profile or admins can view all" ON public.profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

-- Drop any existing assessment policies
DROP POLICY IF EXISTS "Allow read access to assessments" ON public.assessments;
DROP POLICY IF EXISTS "Restrict write access to assessments" ON public.assessments;
DROP POLICY IF EXISTS "Allow read access to assessment questions" ON public.assessment_questions;
DROP POLICY IF EXISTS "Restrict write access to assessment questions" ON public.assessment_questions;
DROP POLICY IF EXISTS "Allow read access to quick answers" ON public.assessment_quick_answers;
DROP POLICY IF EXISTS "Restrict write access to quick answers" ON public.assessment_quick_answers;
DROP POLICY IF EXISTS "Users can view own assessment results" ON public.assessment_results;
DROP POLICY IF EXISTS "Users can create own assessment results" ON public.assessment_results;

-- Drop any existing team policies
DROP POLICY IF EXISTS "Restrict team access until schema fixed" ON public.teams;
DROP POLICY IF EXISTS "Restrict team member access until schema fixed" ON public.team_members;

-- =============================================================================
-- 2. ENABLE ROW LEVEL SECURITY ON ALL PUBLIC TABLES
-- =============================================================================

-- Enable RLS on profiles table (already has policies but RLS not enabled)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Enable RLS on assessment-related tables
ALTER TABLE public.assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assessment_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assessment_quick_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assessment_results ENABLE ROW LEVEL SECURITY;

-- Enable RLS on team-related tables
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- 3. CREATE SIMPLE RLS POLICIES FOR PROFILES TABLE (NO RECURSION)
-- =============================================================================

-- Profiles: Users can only view and update their own profile
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT TO authenticated
    USING (id = auth.uid());

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE TO authenticated
    USING (id = auth.uid());

-- =============================================================================
-- 4. CREATE RLS POLICIES FOR ASSESSMENT TABLES
-- =============================================================================

-- Assessments: Allow read access to all authenticated users, restrict write access
CREATE POLICY "Allow read access to assessments" ON public.assessments
    FOR SELECT TO authenticated
    USING (is_active = true);

-- Note: Write access to assessments should be handled through admin interface
-- For now, we'll restrict all write operations to prevent unauthorized changes
CREATE POLICY "Restrict write access to assessments" ON public.assessments
    FOR ALL TO authenticated
    USING (false);

-- Assessment Questions: Allow read access to authenticated users
CREATE POLICY "Allow read access to assessment questions" ON public.assessment_questions
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Restrict write access to assessment questions" ON public.assessment_questions
    FOR ALL TO authenticated
    USING (false);

-- Assessment Quick Answers: Allow read access to authenticated users
CREATE POLICY "Allow read access to quick answers" ON public.assessment_quick_answers
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Restrict write access to quick answers" ON public.assessment_quick_answers
    FOR ALL TO authenticated
    USING (false);

-- Assessment Results: Users can only access their own results
CREATE POLICY "Users can view own assessment results" ON public.assessment_results
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.chat_sessions
            WHERE chat_sessions.id = assessment_results.session_id
            AND chat_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create own assessment results" ON public.assessment_results
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.chat_sessions
            WHERE chat_sessions.id = assessment_results.session_id
            AND chat_sessions.user_id = auth.uid()
        )
    );

-- =============================================================================
-- 5. CREATE RLS POLICIES FOR TEAM TABLES
-- =============================================================================

-- Note: team_members.user_id is INTEGER, but profiles.id is UUID
-- This indicates a schema mismatch - teams use integer IDs, profiles use UUIDs
-- For now, we'll create restrictive policies until the schema is aligned

-- Teams: Restrict access until schema is fixed
CREATE POLICY "Restrict team access until schema fixed" ON public.teams
    FOR ALL TO authenticated
    USING (false);

-- Team Members: Restrict access until schema is fixed
CREATE POLICY "Restrict team member access until schema fixed" ON public.team_members
    FOR ALL TO authenticated
    USING (false);

-- TODO: Fix schema mismatch between team_members.user_id (integer) and profiles.id (UUID)
-- Options:
-- 1. Change team_members.user_id to UUID type
-- 2. Add integer user_id field to profiles table
-- 3. Create mapping table between integer IDs and UUIDs

-- =============================================================================
-- 6. ADMIN ACCESS SOLUTION
-- =============================================================================

-- For admin access, you can temporarily disable RLS for specific operations:
-- Example: SET row_security = off; (requires superuser privileges)
-- Or create a separate admin schema with different permissions

-- =============================================================================
-- 7. FIX SECURITY DEFINER VIEW (OPTIONAL - DEPENDS ON REQUIREMENTS)
-- =============================================================================

-- Option 1: Remove SECURITY DEFINER (recommended for most cases)
-- This will make the view use the permissions of the querying user
DROP VIEW IF EXISTS public.chat_sessions_with_stats;
CREATE VIEW public.chat_sessions_with_stats AS
SELECT 
    cs.*,
    COUNT(m.id) as message_count,
    MAX(m.created_at) as last_message_at
FROM public.chat_sessions cs
LEFT JOIN public.messages m ON cs.id = m.chat_session_id
GROUP BY cs.id, cs.user_id, cs.title, cs.icon_name, cs.created_at, cs.updated_at, 
         cs.assistant_id, cs.assistant_name, cs.assistant_emoji, cs.assessment_id, 
         cs.assessment_title, cs.assessment_icon, cs.team_id;

-- Option 2: Keep SECURITY DEFINER but add RLS policy (uncomment if needed)
-- CREATE POLICY "Users can view own chat sessions with stats" ON public.chat_sessions_with_stats
--     FOR SELECT TO authenticated
--     USING (user_id = auth.uid());

-- =============================================================================
-- 8. VERIFY SECURITY SETUP
-- =============================================================================

-- Check that RLS is enabled on all tables
SELECT 
    schemaname, 
    tablename, 
    rowsecurity as rls_enabled,
    CASE 
        WHEN rowsecurity THEN '✅ RLS Enabled'
        ELSE '❌ RLS Disabled'
    END as status
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN (
        'profiles', 'assessments', 'assessment_questions',
        'assessment_quick_answers', 'assessment_results',
        'teams', 'team_members'
    )
ORDER BY tablename;

-- Check policies exist
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;
