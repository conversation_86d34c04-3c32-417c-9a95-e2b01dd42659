import React from "react";

interface AssessmentProgressProps {
  current: number;
  total: number;
  percentage: number;
  assessmentTitle?: string;
}

export const AssessmentProgress: React.FC<AssessmentProgressProps> = ({
  current,
  total,
  percentage,
  assessmentTitle = "Assessment",
}) => {
  return (
    <div
      className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-4"
      data-oid="hrz-_-1"
    >
      <div
        className="flex items-center justify-between mb-2"
        data-oid="sow8co:"
      >
        <div className="flex items-center space-x-2" data-oid="8x-wg65">
          <svg
            className="w-4 h-4 text-blue-600 dark:text-blue-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            data-oid="81:6cgz"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              data-oid="bfrjlup"
            />
          </svg>
          <span
            className="text-sm font-medium text-blue-800 dark:text-blue-200"
            data-oid="doftq1l"
          >
            {assessmentTitle} in Progress
          </span>
        </div>
        <span
          className="text-xs text-blue-600 dark:text-blue-400"
          data-oid="kspi3i4"
        >
          {current}/{total} ({Math.round(percentage)}%)
        </span>
      </div>

      <div
        className="w-full bg-blue-100 dark:bg-blue-800 rounded-full h-2"
        data-oid="-6qs9r3"
      >
        <div
          className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${percentage}%` }}
          data-oid="52_gnts"
        />
      </div>
    </div>
  );
};
