// services/providers/geminiProvider.ts
import { GoogleGenAI } from "@google/genai";
import { GEMINI_MODEL_NAME } from '../../constants';
import { AIProviderConfig } from '../aiStreamCore';
import { AIProvider, UnifiedAIResponse } from '../unifiedStreamingService';

/**
 * Gemini Provider Implementation
 */
export class GeminiProvider implements AIProvider {
  private config: AIProviderConfig;

  constructor(config: AIProviderConfig) {
    this.config = config;
  }

  async sendMessage(request: any): Promise<UnifiedAIResponse> {
    console.log(`🤖 [GeminiProvider] Starting request with model: ${request.model || this.config.defaultModel || GEMINI_MODEL_NAME}`);
    const ai = new GoogleGenAI({ apiKey: this.config.apiKey });
    
    try {
      const chat = ai.chats.create({
        model: request.model || this.config.defaultModel || GEMINI_MODEL_NAME,
        config: {
          systemInstruction: request.systemMessage,
          ...(request.search?.web ? { tools: [{ googleSearch: {} }] } : {})
        }
      });

      console.log(`📤 [GeminiProvider] Sending message to Gemini API`);
      // Use the correct format for sending messages
      const result = await chat.sendMessage({ message: request.prompt });
      console.log(`📥 [GeminiProvider] Received result:`, result);
      
      // Check the actual structure of the result
      console.log(`🔍 [GeminiProvider] Result structure:`, {
        hasCandidates: !!result.candidates,
        firstCandidate: result.candidates?.[0],
        candidateKeys: result.candidates?.[0] ? Object.keys(result.candidates[0]) : []
      });
      
      if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
        return {
          text: result.candidates[0].content.parts[0].text,
          metadata: {
            groundingChunks: result.candidates?.[0]?.groundingMetadata?.groundingChunks
          }
        };
      } else {
        console.error(`❌ [GeminiProvider] Invalid response structure:`, result);
        throw new Error('Invalid response format from Gemini API');
      }

    } catch (error) {
      return {
        text: '',
        error: {
          code: 'gemini_error',
          message: error instanceof Error ? error.message : 'Unknown Gemini error',
          retryable: true
        }
      };
    }
  }

  supportsFeature(feature: string): boolean {
    return feature === 'web_search'; // Gemini supports web search
  }
} 