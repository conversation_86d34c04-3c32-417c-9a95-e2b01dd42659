<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assessment System Test - Mobile Optimized</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        };
    </script>
    <style>
        /* Custom styles for mobile optimization */
        .touch-action-pan-y {
            touch-action: pan-y;
        }
        
        .assessment-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .assessment-card:active {
            transform: scale(0.98);
        }
        
        /* Smooth scrolling for mobile */
        .smooth-scroll {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }
        
        /* Custom progress bar animation */
        .progress-bar {
            transition: width 0.5s ease-out;
        }
        
        /* Mobile-friendly button styles */
        .mobile-button {
            min-height: 44px; /* iOS recommended touch target size */
            min-width: 44px;
        }
        
        /* Responsive text sizing */
        @media (max-width: 640px) {
            .responsive-text {
                font-size: 0.875rem;
                line-height: 1.25rem;
            }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-2xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Assessment System Test
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
                Mobile-optimized personality assessment interface
            </p>
        </div>

        <!-- Assessment Selection Cards -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Choose an Assessment
            </h2>
            <div class="grid gap-4">
                <div class="assessment-card bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-lg"
                     onclick="startAssessment('6hn')">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <span class="text-2xl">🧠</span>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                6 Human Needs
                            </h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                Discover your core human needs and motivations
                            </p>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>

                <div class="assessment-card bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-lg opacity-50"
                     onclick="showComingSoon()">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                            <span class="text-2xl">📊</span>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                MBTI Assessment
                            </h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                Myers-Briggs personality type indicator
                            </p>
                        </div>
                        <span class="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded">
                            Coming Soon
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Assessment Question -->
        <div id="assessment-demo" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                <!-- Progress Bar -->
                <div class="mb-6">
                    <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                        <span>Question <span id="current-q">1</span> of <span id="total-q">24</span></span>
                        <span><span id="progress-percent">4</span>% Complete</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div id="progress-bar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 4%"></div>
                    </div>
                </div>

                <!-- Category Badge -->
                <div class="mb-4">
                    <span class="inline-block px-3 py-1 text-xs font-semibold text-blue-800 bg-blue-100 dark:text-blue-200 dark:bg-blue-900 rounded-full">
                        Certainty
                    </span>
                </div>

                <!-- Question -->
                <div class="mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 leading-relaxed">
                        What makes you feel most secure or in control?
                    </h2>

                    <!-- Quick Answers -->
                    <div class="space-y-3">
                        <button class="mobile-button w-full p-4 text-left rounded-lg border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:shadow-md transition-all duration-200"
                                onclick="selectAnswer(this, 1)">
                            <span class="responsive-text">Routines give me stability.</span>
                        </button>
                        
                        <button class="mobile-button w-full p-4 text-left rounded-lg border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:shadow-md transition-all duration-200"
                                onclick="selectAnswer(this, 2)">
                            <span class="responsive-text">I value flexibility over structure.</span>
                        </button>
                        
                        <button class="mobile-button w-full p-4 text-left rounded-lg border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:shadow-md transition-all duration-200"
                                onclick="selectAnswer(this, 3)">
                            <span class="responsive-text">I'm unsure—help me figure it out!</span>
                        </button>
                    </div>
                </div>

                <!-- Follow-up Question (hidden initially) -->
                <div id="follow-up" class="hidden mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-blue-500">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                        How do you typically respond when things feel uncertain or out of control?
                    </h3>
                    <textarea 
                        placeholder="Share your thoughts..."
                        class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
                        rows="4"
                        maxlength="500"
                        oninput="updateCharCount(this)"></textarea>
                    <div class="flex justify-between items-center mt-2">
                        <span id="char-count" class="text-xs text-gray-500 dark:text-gray-400">0/500 characters</span>
                        <button onclick="submitFollowUp()"
                                class="mobile-button px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                            Submit Response
                        </button>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-600">
                    <button onclick="previousQuestion()"
                            class="mobile-button px-6 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                        Previous
                    </button>

                    <button id="next-btn" onclick="nextQuestion()" disabled
                            class="mobile-button px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors">
                        Next Question
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Test Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Mobile Test Controls
            </h3>
            <div class="grid grid-cols-2 gap-4">
                <button onclick="toggleDarkMode()"
                        class="mobile-button px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                    Toggle Dark Mode
                </button>
                
                <button onclick="testTouch()"
                        class="mobile-button px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
                    Test Touch Events
                </button>
                
                <button onclick="simulateProgress()"
                        class="mobile-button px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors">
                    Simulate Progress
                </button>
                
                <button onclick="resetDemo()"
                        class="mobile-button px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                    Reset Demo
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedAnswer = null;
        let currentQuestion = 1;
        let totalQuestions = 24;

        function startAssessment(type) {
            document.getElementById('assessment-demo').classList.remove('hidden');
            document.getElementById('assessment-demo').scrollIntoView({ behavior: 'smooth' });
        }

        function showComingSoon() {
            alert('This assessment type is coming soon!');
        }

        function selectAnswer(button, answerValue) {
            // Remove selection from other buttons
            const buttons = button.parentElement.querySelectorAll('button');
            buttons.forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'text-blue-900', 'dark:text-blue-100');
                btn.classList.add('border-gray-200', 'dark:border-gray-600', 'bg-gray-50', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            });

            // Add selection to clicked button
            button.classList.remove('border-gray-200', 'dark:border-gray-600', 'bg-gray-50', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            button.classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'text-blue-900', 'dark:text-blue-100');

            selectedAnswer = answerValue;
            document.getElementById('next-btn').disabled = false;
            
            // Show follow-up question
            document.getElementById('follow-up').classList.remove('hidden');
        }

        function updateCharCount(textarea) {
            const count = textarea.value.length;
            document.getElementById('char-count').textContent = `${count}/500 characters`;
        }

        function submitFollowUp() {
            alert('Follow-up response submitted!');
        }

        function nextQuestion() {
            if (selectedAnswer) {
                currentQuestion++;
                updateProgress();
                // In a real implementation, this would load the next question
                alert(`Moving to question ${currentQuestion}`);
            }
        }

        function previousQuestion() {
            if (currentQuestion > 1) {
                currentQuestion--;
                updateProgress();
                alert(`Moving to question ${currentQuestion}`);
            }
        }

        function updateProgress() {
            const percentage = Math.round((currentQuestion / totalQuestions) * 100);
            document.getElementById('current-q').textContent = currentQuestion;
            document.getElementById('progress-percent').textContent = percentage;
            document.getElementById('progress-bar').style.width = `${percentage}%`;
        }

        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
        }

        function testTouch() {
            alert('Touch events are working! This alert confirms touch interaction.');
        }

        function simulateProgress() {
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                document.getElementById('progress-bar').style.width = `${progress}%`;
                document.getElementById('progress-percent').textContent = progress;
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        alert('Assessment completed! 🎉');
                    }, 500);
                }
            }, 200);
        }

        function resetDemo() {
            selectedAnswer = null;
            currentQuestion = 1;
            updateProgress();
            document.getElementById('next-btn').disabled = true;
            document.getElementById('follow-up').classList.add('hidden');
            
            // Reset answer buttons
            const buttons = document.querySelectorAll('#assessment-demo button[onclick^="selectAnswer"]');
            buttons.forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'text-blue-900', 'dark:text-blue-100');
                btn.classList.add('border-gray-200', 'dark:border-gray-600', 'bg-gray-50', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            });
        }

        // Initialize
        updateProgress();
    </script>
</body>
</html>
