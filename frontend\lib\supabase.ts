import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY
//const supabaseUrl = process.env.VITE_SUPABASE_URL
//const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types (based on our schema)
export interface Profile {
  id: string
  name: string | null
  email: string | null
  avatar_url: string | null
  role?: 'admin' | 'user'
  last_active?: string
  created_at: string
  updated_at: string
}

export interface UserPreferences {
  id: string
  user_id: string
  theme: 'light' | 'dark' | 'system'
  language: string
  settings: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ChatSession {
  id: string
  user_id: string
  title: string
  icon_name: string
  assistant_id: string | null
  assistant_name: string | null
  assistant_emoji: string | null
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  chat_session_id: string
  content: string
  sender_type: 'user' | 'ai'
  timestamp: string
  metadata: Record<string, any>
  is_loading: boolean
  is_error: boolean
  avatar_url: string | null
  grounding_chunks: any[]
  // Assessment-related fields
  question_id: number | null
  quick_answer_id: number | null
  score: number | null
  is_assessment: boolean
  is_free_text: boolean
}

// Helper functions for common operations
export const supabaseHelpers = {
  // Auth helpers
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },
  
  // User Management helpers
  async getAllUsers() {
    const { data, error } = await supabase.rpc('get_all_users')
    return { data, error }
  },
  
  async createUser(email: string, password: string, name: string, role: 'admin' | 'user' = 'user') {
    const { data, error } = await supabase.rpc('create_user', {
      user_email: email,
      user_password: password,
      user_name: name,
      user_role: role
    })
    return { data, error }
  },
  
  async updateUserRole(userId: string, role: 'admin' | 'user') {
    const { data, error } = await supabase.rpc('update_user_role', {
      user_id: userId,
      new_role: role
    })
    return { data, error }
  },
  
  async deleteUser(userId: string) {
    const { data, error } = await supabase.rpc('delete_user', {
      user_id: userId
    })
    return { data, error }
  },
  
  async updateUser(userId: string, updates: { name?: string, email?: string, avatar_url?: string }) {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    return { data, error }
  },

  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  },

  // Profile helpers
  async getProfile(userId: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    return { data, error }
  },

  async updateProfile(userId: string, updates: Partial<Profile>) {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    return { data, error }
  },
  
  // Set up storage policies for avatars bucket
  async setupAvatarBucketPolicies() {
    try {
      // We can't directly create policies via the JavaScript client
      // But we can make the bucket public which allows read access
      const { error } = await supabase.storage.updateBucket('avatars', {
        public: true,
        fileSizeLimit: 5242880 // 5MB
      });
      
      if (error) {
        console.error("Error updating bucket policies:", error);
        return false;
      }
      
      return true;
    } catch (err) {
      console.error("Error setting up bucket policies:", err);
      return false;
    }
  },

  // Profile picture upload
  async uploadProfilePicture(userId: string, file: File) {
    try {
      // For development/testing purposes, we'll use a data URL approach instead of storage
      // This avoids the need for setting up storage buckets and policies
      return new Promise<{ data: { path: string, url: string } | null, error: Error | null }>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          // The data URL can be used directly as the avatar URL
          const dataUrl = reader.result as string;
          
          // In a real production app, you would upload to Supabase storage here
          // For now, we'll just use the data URL
          console.log("Generated data URL for profile picture");
          
          resolve({
            data: {
              path: `local-avatar-${userId}`,
              url: dataUrl
            },
            error: null
          });
        };
        
        reader.onerror = () => {
          console.error("Error reading file:", reader.error);
          resolve({
            data: null,
            error: new Error("Failed to read image file")
          });
        };
        
        // Read the file as a data URL
        reader.readAsDataURL(file);
      });
    } catch (err) {
      console.error("Unexpected error in uploadProfilePicture:", err);
      return { data: null, error: err as Error };
    }
  },
  
  // Change password
  async changePassword(currentPassword: string, newPassword: string) {
    // First verify current password
    const { data: { session }, error: verifyError } = await supabase.auth.signInWithPassword({
      email: (await supabase.auth.getUser()).data.user?.email || '',
      password: currentPassword
    });
    
    if (verifyError) return { data: null, error: verifyError };
    
    // Update password
    const { data, error } = await supabase.auth.updateUser({
      password: newPassword
    });
    
    return { data, error };
  },

  // Chat helpers
  async getChatSessions(userId: string) {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
    return { data, error }
  },

  async createChatSession(userId: string, title: string, iconName: string = 'SpeechBubbleIcon') {
    const { data, error } = await supabase
      .from('chat_sessions')
      .insert({
        user_id: userId,
        title,
        icon_name: iconName
      })
      .select()
      .single()
    return { data, error }
  },

  async updateChatSession(chatId: string, updates: Partial<ChatSession>) {
    const { data, error } = await supabase
      .from('chat_sessions')
      .update(updates)
      .eq('id', chatId)
      .select()
      .single()
    return { data, error }
  },

  async deleteChatSession(chatId: string) {
    const { error } = await supabase
      .from('chat_sessions')
      .delete()
      .eq('id', chatId)
    return { error }
  },

  // Message helpers
  async getMessages(chatSessionId: string) {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('chat_session_id', chatSessionId)
      .order('timestamp', { ascending: true })
    return { data, error }
  },

  async addMessage(message: any) {
    const { data, error } = await supabase
      .from('messages')
      .insert(message)
      .select()
      .single()

    if (error) {
      console.error('❌ [supabaseHelpers.addMessage] Database error:', error);
    } else {
      console.log('✅ [supabaseHelpers.addMessage] Message inserted successfully:', data?.id);
    }

    return { data, error }
  },

  async updateMessage(messageId: string, updates: Partial<Message>) {
    const { data, error } = await supabase
      .from('messages')
      .update(updates)
      .eq('id', messageId)
      .select()
      .single()
    return { data, error }
  },

  // Real-time subscriptions
  subscribeToChatMessages(chatSessionId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`messages:${chatSessionId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'messages',
          filter: `chat_session_id=eq.${chatSessionId}`
        },
        callback
      )
      .subscribe()
  },

  subscribeToChatSessions(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`chat_sessions:${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chat_sessions',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe()
  }
}
