import React, { useState, useEffect } from "react";
import { supabase } from "../lib/supabase";

// Debug component to test Supabase connection and session handling
const ResetPasswordDebug: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [accessToken, setAccessToken] = useState("");
  const [refreshToken, setRefreshToken] = useState("");

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs((prev) => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[ResetPasswordDebug] ${message}`);
  };

  useEffect(() => {
    addLog("Debug component mounted");

    // Log current URL for debugging
    addLog(`Current URL: ${window.location.href}`);
    addLog(`URL hash: ${window.location.hash}`);
    addLog(`URL search: ${window.location.search}`);

    // Check URL hash for tokens
    if (window.location.hash) {
      const hash = window.location.hash.substring(1);
      addLog(`Raw hash: ${hash}`);
      const params = new URLSearchParams(hash);
      const access = params.get("access_token");
      const refresh = params.get("refresh_token");
      const type = params.get("type");

      addLog(
        `URL hash found - type: ${type}, access_token: ${!!access}, refresh_token: ${!!refresh}`,
      );

      if (access) setAccessToken(access);
      if (refresh) setRefreshToken(refresh);
    } else {
      addLog("No URL hash found");
    }

    // Check URL search params as fallback
    if (window.location.search) {
      const searchParams = new URLSearchParams(window.location.search);
      const access = searchParams.get("access_token");
      const refresh = searchParams.get("refresh_token");
      const type = searchParams.get("type");

      addLog(
        `URL search found - type: ${type}, access_token: ${!!access}, refresh_token: ${!!refresh}`,
      );

      if (access && !accessToken) setAccessToken(access);
      if (refresh && !refreshToken) setRefreshToken(refresh);
    }

    // Check sessionStorage
    const storedAccess = sessionStorage.getItem(
      "supabase_recovery_access_token",
    );
    const storedRefresh = sessionStorage.getItem(
      "supabase_recovery_refresh_token",
    );
    const storedType = sessionStorage.getItem("supabase_recovery_type");
    addLog(
      `SessionStorage - type: ${storedType}, access_token: ${!!storedAccess}, refresh_token: ${!!storedRefresh}`,
    );

    if (storedAccess && !accessToken) setAccessToken(storedAccess);
    if (storedRefresh && !refreshToken) setRefreshToken(storedRefresh);
  }, []);

  const testSupabaseConnection = async () => {
    addLog("Testing Supabase connection...");
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        addLog(`Supabase connection error: ${error.message}`);
      } else {
        addLog(
          `Supabase connection OK - session: ${data.session ? "exists" : "null"}`,
        );
      }
    } catch (err) {
      addLog(`Supabase connection exception: ${err}`);
    }
  };

  const testSetSession = async () => {
    if (!accessToken) {
      addLog("No access token available for session test");
      return;
    }

    addLog("Testing session establishment...");
    try {
      const { data, error } = await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken || "",
      });

      if (error) {
        addLog(`Session establishment error: ${error.message}`);
      } else {
        addLog(`Session established successfully - user: ${data.user?.email}`);
      }
    } catch (err) {
      addLog(`Session establishment exception: ${err}`);
    }
  };

  const testPasswordUpdate = async () => {
    addLog("Testing password update (with dummy password)...");
    try {
      // First ensure session is set
      if (accessToken) {
        await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken || "",
        });
      }

      const { error, data } = await supabase.auth.updateUser({
        password: "test123456", // Dummy password for testing
      });

      if (error) {
        addLog(`Password update error: ${error.message}`);
      } else {
        addLog(`Password update would succeed - user: ${data.user?.email}`);
      }
    } catch (err) {
      addLog(`Password update exception: ${err}`);
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 min-h-screen"
      data-oid="-g4.-cu"
    >
      <div
        className="w-full max-w-2xl mx-auto my-auto rounded-2xl bg-white dark:bg-slate-800 border-b-2 border-black/10 p-6 relative shadow-xl"
        data-oid="zqh__3v"
      >
        <h2 className="text-xl font-bold mb-4" data-oid="xo0:xe9">
          Password Reset Debug
        </h2>

        <div className="mb-4 space-x-2" data-oid="y4zqy:e">
          <button
            onClick={testSupabaseConnection}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
            data-oid="-itdvgl"
          >
            Test Connection
          </button>
          <button
            onClick={testSetSession}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm"
            data-oid="np07.jf"
          >
            Test Session
          </button>
          <button
            onClick={testPasswordUpdate}
            className="px-3 py-1 bg-orange-500 text-white rounded text-sm"
            data-oid="h9luuws"
          >
            Test Password Update
          </button>
          <button
            onClick={() => setLogs([])}
            className="px-3 py-1 bg-gray-500 text-white rounded text-sm"
            data-oid="ywz:y_-"
          >
            Clear Logs
          </button>
        </div>

        <div className="mb-4" data-oid="ivpqpqa">
          <div className="text-sm" data-oid="zpkbkpv">
            <strong data-oid="if46e0j">Access Token:</strong>{" "}
            {accessToken ? `${accessToken.substring(0, 20)}...` : "None"}
          </div>
          <div className="text-sm" data-oid="f8h9z8u">
            <strong data-oid="ab8stc6">Refresh Token:</strong>{" "}
            {refreshToken ? `${refreshToken.substring(0, 20)}...` : "None"}
          </div>
        </div>

        <div
          className="bg-gray-100 dark:bg-gray-700 p-3 rounded max-h-64 overflow-y-auto"
          data-oid="fgtvc-4"
        >
          <div className="text-sm font-mono" data-oid="f08rchm">
            {logs.length === 0
              ? "No logs yet..."
              : logs.map((log, index) => (
                  <div key={index} className="mb-1" data-oid="h1d4qtq">
                    {log}
                  </div>
                ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordDebug;
