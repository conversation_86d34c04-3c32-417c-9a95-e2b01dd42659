// services/providers/openRouterProvider.ts
import { AIProviderConfig } from '../aiStreamCore';
import { AIProvider, UnifiedAIResponse } from '../unifiedStreamingService';

/**
 * OpenRouter Provider Implementation
 */
export class OpenRouterProvider implements AIProvider {
  private apiKey: string;
  private baseUrl: string;
  private defaultModel: string;

  constructor(config: AIProviderConfig) {
    this.apiKey = config.apiKey;
    this.defaultModel = config.defaultModel || 'google/gemini-pro';
    this.baseUrl = 'https://openrouter.ai/api/v1';
  }

  async sendMessage(request: any): Promise<UnifiedAIResponse> {
    try {
      // Prepare messages array with system message if present
      const messages = [];
      if (request.systemMessage) {
        messages.push({ role: 'system', content: request.systemMessage });
      }
      messages.push({ role: 'user', content: request.prompt });

      // OpenRouter API request
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'AI Chat Interface'
        },
        body: JSON.stringify({
          model: request.model || this.defaultModel,
          messages: messages,
          stream: false, // Get complete response
          temperature: request.temperature || 0.7,
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error(`❌ [OpenRouterProvider] API error:`, {
          status: response.status,
          statusText: response.statusText,
          errorData
        });
        throw new Error(
          `OpenRouter API error: ${response.status} - ${errorData.error?.message || errorData.message || 'Unknown error'}`
        );
      }

      const data = await response.json();
      const choice = data.choices?.[0];
      
      if (choice?.message?.content) {
        return {
          text: choice.message.content,
          metadata: {
            usage: data.usage ? {
              promptTokens: data.usage.prompt_tokens,
              completionTokens: data.usage.completion_tokens
            } : undefined
          }
        };
      } else {
        throw new Error('Invalid response format from OpenRouter API');
      }

    } catch (error) {
      return {
        text: '',
        error: {
          code: 'openrouter_error',
          message: error instanceof Error ? error.message : 'Unknown OpenRouter error',
          retryable: true
        }
      };
    }
  }

  supportsFeature(feature: string): boolean {
    return false; // OpenRouter doesn't support web search in this implementation
  }
}
