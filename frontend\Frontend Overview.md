## 📋 **Frontend Overview**

**Technology Stack:**

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite 6.2.0
- **Styling**: Tailwind CSS v4.1.11
- **Backend**: Supabase (PostgreSQL + Auth)
- **Routing**: React Router DOM v6
- **State Management**: Local state with React hooks
- **Internationalization**: react-i18next
- **AI Integration**: Google Gemini API + OpenRouter

## 🏗️ **Architecture & Structure**

**Main Application Structure:**

`frontend/
├── components/          # 42 React components
├── services/           # API & business logic services  
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── data/               # Static data files
└── lib/                # Third-party integrations`

**Key Components:**

- `App.tsx` - Main application with routing & state management
- `MainArea.tsx` - Chat interface with AI streaming
- `Sidebar.tsx` - Navigation with chat history & assistants
- `MessageDisplay.tsx` - Chat message rendering
- `ChatInput.tsx` - User input handling

## 🔧 **Key Features Implemented**

**Authentication & User Management:**

- <PERSON><PERSON><PERSON> Auth integration (email/password, OAuth)
- User profiles with avatar support
- Admin user management system
- Role-based access control

**Chat System:**

- Real-time AI chat with streaming responses
- Multiple AI models (Gemini, OpenRouter providers)
- Chat history with date grouping
- Message persistence in Supabase
- Assistant/persona system

**Assessment System:**

- Personality assessment integration
- Interactive questionnaires
- Progress tracking
- Results visualization

**UI/UX Features:**

- Dark/light theme support
- Responsive design (mobile-first)
- Internationalization (i18n)
- Custom scrollbars
- Loading states & error handling

## 🔌 **Services & Integrations**

**Core Services:**

- `dataService` - Supabase database operations
- `geminiService` - Google AI integration
- `aiService` - Unified AI streaming
- `assessmentService` - Personality assessments
- `assistantService` - AI assistant management

**External APIs:**

- Google Gemini 2.0 Flash
- OpenRouter (multiple AI models)
- Supabase (auth, database, storage)

## 📱 **Responsive Design**

The app is fully responsive with:

- Mobile-first approach
- Collapsible sidebar on small screens
- Touch-friendly interface
- Adaptive layouts for different screen sizes

## 🎨 **Styling System**

- **Tailwind CSS v4** for utility-first styling
- **Dark mode** support with system preference detection
- **Custom CSS** for message content styling
- **Consistent color scheme** (slate-based palette)

## 🔒 **Security & Performance**

- Environment variables for API keys
- Authentication state management
- Connection monitoring & retry logic
- Message deduplication
- Optimized re-renders with useCallback/useMemo

## 🚀 **Development Features**

- **TypeScript** for type safety
- **Hot module replacement** with Vite
- **Translation management** scripts
- **Debug logging** throughout the application
- **Error boundaries** and error handling

The frontend architecture employs modern React patterns with comprehensive TypeScript typing and clean separation of concerns. The codebase demonstrates best practices in state management, error handling, and user experience optimization.
