import React, { useState, useEffect } from "react";
import { connectionService } from "../services/connectionService";
import { networkService, NetworkQuality } from "../services/networkService";
import { offlineQueueService } from "../services/offlineQueueService";
import { useTranslation } from "react-i18next";
import { Tooltip, Badge } from "@mui/material";

/**
 * Props for the ConnectionStatusIndicator component
 */
interface ConnectionStatusIndicatorProps {
  /** Whether to show the indicator text (default: true) */
  showText?: boolean;
  /** Whether to show the indicator icon (default: true) */
  showIcon?: boolean;
  /** Whether to show detailed information in a tooltip (default: true) */
  showDetails?: boolean;
  /** Whether to show offline status (default: true) */
  showOffline?: boolean;
  /** Whether to show poor connection status (default: true) */
  showPoor?: boolean;
  /** CSS class name for the container */
  className?: string;
}

/**
 * A component that displays the current connection status
 */
const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({
  showText = true,
  showIcon = true,
  showDetails = true,
  showOffline = true,
  showPoor = true,
  className = "",
}) => {
  const { t } = useTranslation();
  const [isConnected, setIsConnected] = useState<boolean>(
    connectionService.isConnectionActive(),
  );
  const [networkQuality, setNetworkQuality] = useState<NetworkQuality>(
    NetworkQuality.UNKNOWN,
  );
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [pendingOperations, setPendingOperations] = useState<number>(0);
  const [isRecovering, setIsRecovering] = useState<boolean>(false);
  const [recoveryAttempt, setRecoveryAttempt] = useState<number>(0);

  // Hide the indicator after a delay when connection is restored
  useEffect(() => {
    let hideTimeout: number | null = null;

    if (isConnected) {
      // When connected, show for 5 seconds then hide
      setIsVisible(true);
      hideTimeout = window.setTimeout(() => {
        setIsVisible(false);
      }, 5000);
    } else {
      // When disconnected, always show
      setIsVisible(true);
      if (hideTimeout) {
        window.clearTimeout(hideTimeout);
        hideTimeout = null;
      }
    }

    return () => {
      if (hideTimeout) {
        window.clearTimeout(hideTimeout);
      }
    };
  }, [isConnected]);

  // Listen for connection status changes
  useEffect(() => {
    const handleConnectionChange = (connected: boolean) => {
      setIsConnected(connected);
    };

    const handleNetworkStatusChange = (status: any) => {
      setNetworkQuality(status.quality);
    };

    // Custom event listener for connection recovery attempts
    const handleRecoveryAttempt = (event: CustomEvent) => {
      setIsRecovering(true);
      setRecoveryAttempt(event.detail?.attempt || 0);
    };

    // Custom event listener for connection recovery success
    const handleRecoverySuccess = () => {
      setIsRecovering(false);
      setRecoveryAttempt(0);
    };

    // Add listeners
    connectionService.addConnectionListener(handleConnectionChange);
    networkService.addStatusListener(handleNetworkStatusChange);

    // Add custom event listeners
    window.addEventListener(
      "connection_recovery_attempt",
      handleRecoveryAttempt as EventListener,
    );
    window.addEventListener(
      "connection_recovery_success",
      handleRecoverySuccess as EventListener,
    );

    // Initial status check
    setIsConnected(connectionService.isConnectionActive());
    setNetworkQuality(networkService.getNetworkStatus().quality);

    // Check for pending operations
    const checkPendingOperations = () => {
      const counts = offlineQueueService.getOperationCounts();
      setPendingOperations(counts.PENDING + counts.PROCESSING);
    };

    // Initial check and set up interval
    checkPendingOperations();
    const intervalId = setInterval(checkPendingOperations, 5000);

    // Remove listeners on cleanup
    return () => {
      connectionService.removeConnectionListener(handleConnectionChange);
      networkService.removeStatusListener(handleNetworkStatusChange);
      window.removeEventListener(
        "connection_recovery_attempt",
        handleRecoveryAttempt as EventListener,
      );
      window.removeEventListener(
        "connection_recovery_success",
        handleRecoverySuccess as EventListener,
      );
      clearInterval(intervalId);
    };
  }, []);

  // Don't render anything if not visible
  if (!isVisible) {
    return null;
  }

  // Determine status text and styles
  const getStatusInfo = () => {
    // If recovering, show recovery status
    if (isRecovering) {
      return {
        text: t("connection.recovering"),
        iconClass: "bg-yellow-500 animate-pulse",
        textClass: "text-yellow-500",
        detailText: t("connection.recoveryAttempt", {
          attempt: recoveryAttempt,
        }),
      };
    }

    // If offline, show offline status
    if (!isConnected) {
      return {
        text: t("connection.offline"),
        iconClass: "bg-red-500",
        textClass: "text-red-500",
        detailText: t("connection.offlineDetail"),
      };
    }

    // If there are pending operations, show syncing status
    if (pendingOperations > 0) {
      return {
        text: t("connection.syncing"),
        iconClass: "bg-blue-500 animate-pulse",
        textClass: "text-blue-500",
        detailText: t("connection.pendingOperations", {
          count: pendingOperations,
        }),
      };
    }

    // Otherwise, show network quality
    switch (networkQuality) {
      case NetworkQuality.POOR:
        return {
          text: t("connection.poor"),
          iconClass: "bg-orange-500",
          textClass: "text-orange-500",
          detailText: t("connection.poorDetail"),
        };
      case NetworkQuality.FAIR:
        return {
          text: t("connection.fair"),
          iconClass: "bg-yellow-500",
          textClass: "text-yellow-500",
          detailText: t("connection.fairDetail"),
        };
      case NetworkQuality.GOOD:
        return {
          text: t("connection.good"),
          iconClass: "bg-green-500",
          textClass: "text-green-500",
          detailText: t("connection.goodDetail"),
        };
      case NetworkQuality.EXCELLENT:
        return {
          text: t("connection.excellent"),
          iconClass: "bg-green-500",
          textClass: "text-green-500",
          detailText: t("connection.excellentDetail"),
        };
      default:
        return {
          text: isConnected
            ? t("connection.connected")
            : t("connection.offline"),
          iconClass: isConnected ? "bg-green-500" : "bg-red-500",
          textClass: isConnected ? "text-green-500" : "text-red-500",
          detailText: isConnected
            ? t("connection.connectedDetail")
            : t("connection.offlineDetail"),
        };
    }
  };

  const { text, iconClass, textClass, detailText } = getStatusInfo();

  const indicator = (
    <div className={`flex items-center ${className}`} data-oid="tyidivs">
      {showIcon &&
        (pendingOperations > 0 ? (
          <Badge
            badgeContent={pendingOperations}
            color="primary"
            className="mr-2"
            data-oid="o35d3cz"
          >
            <div
              className={`h-2 w-2 rounded-full ${iconClass}`}
              data-oid="r2mlq87"
            ></div>
          </Badge>
        ) : (
          <div
            className={`h-2 w-2 rounded-full ${iconClass} mr-2`}
            data-oid="l65o-ki"
          ></div>
        ))}
      {showText && (
        <span className={`text-sm font-medium ${textClass}`} data-oid="d6yqs-m">
          {text}
        </span>
      )}
      {pendingOperations > 0 && !showIcon && (
        <span
          className="ml-2 text-xs bg-blue-100 text-blue-800 rounded-full px-2 py-0.5"
          data-oid="_t6s-gj"
        >
          {pendingOperations}
        </span>
      )}
    </div>
  );

  // If details are enabled, wrap in a tooltip
  if (showDetails && detailText) {
    return (
      <Tooltip title={detailText} arrow placement="bottom" data-oid="qwtm5de">
        <div data-oid="svdg0ac">{indicator}</div>
      </Tooltip>
    );
  }

  return indicator;
};

export default ConnectionStatusIndicator;
