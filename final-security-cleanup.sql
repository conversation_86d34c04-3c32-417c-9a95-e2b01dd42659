-- FINAL Security Cleanup - Fix remaining security advisor issues
-- This script addresses the persistent security definer view issue

-- =============================================================================
-- 1. FORCE FIX SECURITY DEFINER VIEW
-- =============================================================================

-- Drop the view completely first
DROP VIEW IF EXISTS public.chat_sessions_with_stats CASCADE;

-- Wait a moment and recreate without any security definer properties
-- Use a simple approach to ensure no security definer is applied
CREATE OR REPLACE VIEW public.chat_sessions_with_stats 
WITH (security_invoker=true)
AS
SELECT 
    cs.id,
    cs.user_id,
    cs.title,
    cs.icon_name,
    cs.created_at,
    cs.updated_at,
    cs.assistant_id,
    cs.assistant_name,
    cs.assistant_emoji,
    cs.assessment_id,
    cs.assessment_title,
    cs.assessment_icon,
    cs.team_id,
    COALESCE(message_stats.message_count, 0) as message_count,
    message_stats.last_message_at
FROM public.chat_sessions cs
LEFT JOIN (
    SELECT 
        chat_session_id,
        COUNT(id) as message_count,
        MAX(timestamp) as last_message_at
    FROM public.messages 
    GROUP BY chat_session_id
) message_stats ON cs.id = message_stats.chat_session_id;

-- =============================================================================
-- 2. VERIFY VIEW PROPERTIES
-- =============================================================================

-- Check the view properties to ensure it's not security definer
SELECT 
    schemaname,
    viewname,
    definition
FROM pg_views 
WHERE schemaname = 'public' 
    AND viewname = 'chat_sessions_with_stats';

-- =============================================================================
-- 3. ALTERNATIVE: CREATE FUNCTION INSTEAD OF VIEW (IF NEEDED)
-- =============================================================================

-- If the view keeps getting security definer, create a function instead
CREATE OR REPLACE FUNCTION public.get_chat_sessions_with_stats()
RETURNS TABLE (
    id uuid,
    user_id uuid,
    title text,
    icon_name text,
    created_at timestamptz,
    updated_at timestamptz,
    assistant_id uuid,
    assistant_name text,
    assistant_emoji text,
    assessment_id integer,
    assessment_title character varying(255),
    assessment_icon character varying(255),
    team_id integer,
    message_count bigint,
    last_message_at timestamptz
)
LANGUAGE sql
SECURITY INVOKER
AS $$
    SELECT 
        cs.id,
        cs.user_id,
        cs.title,
        cs.icon_name,
        cs.created_at,
        cs.updated_at,
        cs.assistant_id,
        cs.assistant_name,
        cs.assistant_emoji,
        cs.assessment_id,
        cs.assessment_title,
        cs.assessment_icon,
        cs.team_id,
        COALESCE(message_stats.message_count, 0) as message_count,
        message_stats.last_message_at
    FROM public.chat_sessions cs
    LEFT JOIN (
        SELECT 
            chat_session_id,
            COUNT(id) as message_count,
            MAX(timestamp) as last_message_at
        FROM public.messages 
        GROUP BY chat_session_id
    ) message_stats ON cs.id = message_stats.chat_session_id;
$$;

-- =============================================================================
-- 4. FINAL STATUS CHECK
-- =============================================================================

-- Check remaining security issues
SELECT 
    'Security Definer Views' as check_type,
    COUNT(*) as count
FROM information_schema.views 
WHERE table_schema = 'public' 
    AND view_definition LIKE '%SECURITY DEFINER%';

-- Success message
SELECT '🎉 Final security cleanup completed! Check security advisor again.' as result;
