// Simple test to verify the health check function works from the frontend
import { supabase } from './lib/supabase.js';

async function testHealthCheck() {
  console.log('🔍 Testing health check function...');
  
  try {
    const start = Date.now();
    const { data, error } = await supabase.rpc('health_check');
    const elapsed = Date.now() - start;
    
    if (error) {
      console.error('❌ Health check failed:', error);
      return false;
    }
    
    console.log(`✅ Health check successful in ${elapsed}ms:`, data);
    return true;
  } catch (err) {
    console.error('❌ Health check error:', err);
    return false;
  }
}

// Run the test
testHealthCheck().then(success => {
  console.log(success ? '🎉 Health check test passed!' : '💥 Health check test failed!');
});
