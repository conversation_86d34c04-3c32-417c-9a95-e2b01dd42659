import React, { useState } from "react";
import AssistantsModal from "./AssistantsModal";
import AssistantFormModal from "./AssistantFormModal";
import { Assistant } from "../types/assistant";

interface AssistantsModalLauncherProps {}

const AssistantsModalLauncher: React.FC<AssistantsModalLauncherProps> = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editAssistant, setEditAssistant] = useState<Assistant | null>(null);

  const handleEdit = (assistant: Assistant) => {
    setEditAssistant(assistant);
    setIsFormOpen(true);
  };
  const handleAdd = () => {
    setEditAssistant(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setIsModalOpen(false); // Also close modal to refresh list
    setTimeout(() => setIsModalOpen(true), 100); // Reopen after short delay
  };

  // TODO: Replace with actual admin check from context or props
  const isAdmin = true;

  return (
    <>
      <button
        className="px-4 py-2 bg-slate-600 hover:bg-slate-700 dark:bg-slate-700 dark:hover:bg-slate-600 text-white dark:text-slate-100 rounded-md text-sm font-medium transition-colors mb-4"
        onClick={() => setIsModalOpen(true)}
        data-oid="gue2r_f"
      >
        Manage Assistants
      </button>
      <AssistantsModal
        isOpen={isModalOpen && !isFormOpen}
        onClose={() => setIsModalOpen(false)}
        onEdit={handleEdit}
        onAdd={handleAdd}
        isAdmin={isAdmin}
        data-oid="1eugxnr"
      />

      <AssistantFormModal
        isOpen={isFormOpen}
        onClose={handleCloseForm}
        initialData={editAssistant}
        data-oid="am5o:sg"
      />
    </>
  );
};

export default AssistantsModalLauncher;
