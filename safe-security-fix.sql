-- SAFE Security Fix - Handles existing policies gracefully
-- This script can be run multiple times safely

-- =============================================================================
-- 1. CLEAN UP EXISTING POLICIES TO PREVENT CONFLICTS
-- =============================================================================

-- Drop all existing policies that might cause issues
DROP POLICY IF EXISTS "Users can update own profile or admins can update all" ON public.profiles;
DROP POLICY IF EXISTS "Users can view own profile or admins can view all" ON public.profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

-- Assessment policies
DROP POLICY IF EXISTS "Allow read access to assessments" ON public.assessments;
DROP POLICY IF EXISTS "Restrict write access to assessments" ON public.assessments;
DROP POLICY IF EXISTS "Allow admin write access to assessments" ON public.assessments;
DROP POLICY IF EXISTS "Allow read access to assessment questions" ON public.assessment_questions;
DROP POLICY IF EXISTS "Restrict write access to assessment questions" ON public.assessment_questions;
DROP POLICY IF EXISTS "Allow admin write access to assessment questions" ON public.assessment_questions;
DROP POLICY IF EXISTS "Allow read access to quick answers" ON public.assessment_quick_answers;
DROP POLICY IF EXISTS "Restrict write access to quick answers" ON public.assessment_quick_answers;
DROP POLICY IF EXISTS "Allow admin write access to quick answers" ON public.assessment_quick_answers;
DROP POLICY IF EXISTS "Users can view own assessment results" ON public.assessment_results;
DROP POLICY IF EXISTS "Users can create own assessment results" ON public.assessment_results;

-- Team policies
DROP POLICY IF EXISTS "Restrict team access until schema fixed" ON public.teams;
DROP POLICY IF EXISTS "Restrict team member access until schema fixed" ON public.team_members;
DROP POLICY IF EXISTS "Users can view teams they belong to" ON public.teams;
DROP POLICY IF EXISTS "Restrict team write access" ON public.teams;
DROP POLICY IF EXISTS "Users can view team memberships" ON public.team_members;
DROP POLICY IF EXISTS "Restrict team membership write access" ON public.team_members;

-- =============================================================================
-- 2. ENABLE ROW LEVEL SECURITY ON ALL TABLES
-- =============================================================================

-- Enable RLS (safe to run multiple times)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assessment_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assessment_quick_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assessment_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- 3. CREATE SIMPLE, NON-RECURSIVE POLICIES
-- =============================================================================

-- Profiles: Simple policies without recursion
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT TO authenticated
    USING (id = auth.uid());

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE TO authenticated
    USING (id = auth.uid());

-- Assessments: Read-only for authenticated users
CREATE POLICY "Allow read access to assessments" ON public.assessments
    FOR SELECT TO authenticated
    USING (is_active = true);

CREATE POLICY "Restrict write access to assessments" ON public.assessments
    FOR ALL TO authenticated
    USING (false);

-- Assessment Questions: Read-only for authenticated users
CREATE POLICY "Allow read access to assessment questions" ON public.assessment_questions
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Restrict write access to assessment questions" ON public.assessment_questions
    FOR ALL TO authenticated
    USING (false);

-- Assessment Quick Answers: Read-only for authenticated users
CREATE POLICY "Allow read access to quick answers" ON public.assessment_quick_answers
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Restrict write access to quick answers" ON public.assessment_quick_answers
    FOR ALL TO authenticated
    USING (false);

-- Assessment Results: Users can only access their own results
CREATE POLICY "Users can view own assessment results" ON public.assessment_results
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.chat_sessions 
            WHERE chat_sessions.id = assessment_results.session_id 
            AND chat_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create own assessment results" ON public.assessment_results
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.chat_sessions 
            WHERE chat_sessions.id = assessment_results.session_id 
            AND chat_sessions.user_id = auth.uid()
        )
    );

-- Teams: Restrictive policies due to schema mismatch
CREATE POLICY "Restrict team access until schema fixed" ON public.teams
    FOR ALL TO authenticated
    USING (false);

CREATE POLICY "Restrict team member access until schema fixed" ON public.team_members
    FOR ALL TO authenticated
    USING (false);

-- =============================================================================
-- 4. FIX SECURITY DEFINER VIEW
-- =============================================================================

-- Remove SECURITY DEFINER from chat_sessions_with_stats view
DROP VIEW IF EXISTS public.chat_sessions_with_stats;
CREATE VIEW public.chat_sessions_with_stats AS
SELECT
    cs.*,
    COUNT(m.id) as message_count,
    MAX(m.timestamp) as last_message_at
FROM public.chat_sessions cs
LEFT JOIN public.messages m ON cs.id = m.chat_session_id
GROUP BY cs.id, cs.user_id, cs.title, cs.icon_name, cs.created_at, cs.updated_at,
         cs.assistant_id, cs.assistant_name, cs.assistant_emoji, cs.assessment_id,
         cs.assessment_title, cs.assessment_icon, cs.team_id;

-- =============================================================================
-- 5. VERIFY SECURITY SETUP
-- =============================================================================

-- Check that RLS is enabled on all tables
SELECT 
    schemaname, 
    tablename, 
    rowsecurity as rls_enabled,
    CASE 
        WHEN rowsecurity THEN '✅ RLS Enabled'
        ELSE '❌ RLS Disabled'
    END as status
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN (
        'profiles', 'assessments', 'assessment_questions', 
        'assessment_quick_answers', 'assessment_results',
        'teams', 'team_members'
    )
ORDER BY tablename;

-- Check policies exist
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Success message
SELECT '🎉 Security fixes applied successfully! Database connection should be restored.' as result;
