<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assessment Streaming Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        #output { background: #f5f5f5; padding: 10px; margin: 10px 0; min-height: 100px; }
        .streaming-text { border: 1px solid #ddd; padding: 10px; margin: 10px 0; min-height: 50px; }
    </style>
</head>
<body>
    <h1>Assessment Streaming Test</h1>
    
    <div class="test-section">
        <h3>1. Authentication Status</h3>
        <button onclick="checkAuth()">Check Authentication</button>
        <div id="auth-status"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Assessment Session Test</h3>
        <button onclick="findAssessmentSession()">Find Assessment Session</button>
        <div id="session-info"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Streaming Test in Assessment Session</h3>
        <input type="text" id="test-message" placeholder="Enter test message" value="Can you explain my results in more detail?">
        <button onclick="testStreaming()">Test Streaming</button>
        <div class="streaming-text" id="streaming-output">Streaming output will appear here...</div>
    </div>
    
    <div class="test-section">
        <h3>4. Assessment Message Types</h3>
        <button onclick="analyzeMessages()">Analyze Message Types</button>
        <div id="message-analysis"></div>
    </div>
    
    <div id="output"></div>

    <script type="module">
        import { supabase } from './lib/supabase.js';
        
        let currentSessionId = null;
        
        window.checkAuth = async () => {
            const statusDiv = document.getElementById('auth-status');
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                if (error) {
                    statusDiv.innerHTML = `<span class="error">❌ Auth Error: ${error.message}</span>`;
                } else if (session) {
                    statusDiv.innerHTML = `<span class="success">✅ Authenticated as: ${session.user.email}</span>`;
                } else {
                    statusDiv.innerHTML = `<span class="error">❌ Not authenticated</span>`;
                }
            } catch (err) {
                statusDiv.innerHTML = `<span class="error">❌ Exception: ${err.message}</span>`;
            }
        };
        
        window.findAssessmentSession = async () => {
            const infoDiv = document.getElementById('session-info');
            try {
                // Find assessment sessions
                const { data: sessions, error } = await supabase
                    .from('chat_sessions')
                    .select('id, title, assessment_id, assessment_title, created_at')
                    .or('assessment_id.not.is.null,title.ilike.%assessment%')
                    .order('created_at', { ascending: false })
                    .limit(5);
                
                if (error) {
                    infoDiv.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
                    return;
                }
                
                if (sessions && sessions.length > 0) {
                    currentSessionId = sessions[0].id;
                    let html = '<span class="success">✅ Found assessment sessions:</span><ul>';
                    sessions.forEach(session => {
                        html += `<li>${session.title} (ID: ${session.id.substring(0, 8)}...)</li>`;
                    });
                    html += '</ul>';
                    html += `<span class="info">Using session: ${currentSessionId.substring(0, 8)}... for testing</span>`;
                    infoDiv.innerHTML = html;
                } else {
                    infoDiv.innerHTML = '<span class="error">❌ No assessment sessions found</span>';
                }
            } catch (err) {
                infoDiv.innerHTML = `<span class="error">❌ Exception: ${err.message}</span>`;
            }
        };
        
        window.testStreaming = async () => {
            const outputDiv = document.getElementById('streaming-output');
            const messageInput = document.getElementById('test-message');
            
            if (!currentSessionId) {
                outputDiv.innerHTML = '<span class="error">❌ No session selected. Find an assessment session first.</span>';
                return;
            }
            
            const message = messageInput.value.trim();
            if (!message) {
                outputDiv.innerHTML = '<span class="error">❌ Please enter a test message</span>';
                return;
            }
            
            outputDiv.innerHTML = '<span class="info">🔄 Testing streaming...</span>';
            
            try {
                // This would normally go through the streaming service
                // For now, let's just test if we can add a message to the session
                const { data: newMessage, error } = await supabase
                    .from('messages')
                    .insert({
                        chat_session_id: currentSessionId,
                        content: message,
                        sender_type: 'user',
                        is_assessment: false
                    })
                    .select()
                    .single();
                
                if (error) {
                    outputDiv.innerHTML = `<span class="error">❌ Error adding message: ${error.message}</span>`;
                } else {
                    outputDiv.innerHTML = `<span class="success">✅ Message added successfully! ID: ${newMessage.id}</span>`;
                    outputDiv.innerHTML += '<br><span class="info">💡 In the real app, this would trigger AI streaming response</span>';
                }
            } catch (err) {
                outputDiv.innerHTML = `<span class="error">❌ Exception: ${err.message}</span>`;
            }
        };
        
        window.analyzeMessages = async () => {
            const analysisDiv = document.getElementById('message-analysis');
            
            if (!currentSessionId) {
                analysisDiv.innerHTML = '<span class="error">❌ No session selected</span>';
                return;
            }
            
            try {
                const { data: messages, error } = await supabase
                    .from('messages')
                    .select('id, content, sender_type, is_assessment, question_id, score, timestamp')
                    .eq('chat_session_id', currentSessionId)
                    .order('timestamp', { ascending: true });
                
                if (error) {
                    analysisDiv.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
                    return;
                }
                
                let html = '<span class="success">✅ Message Analysis:</span><br>';
                html += `<strong>Total messages: ${messages.length}</strong><ul>`;
                
                messages.forEach((msg, index) => {
                    const preview = msg.content.substring(0, 50) + (msg.content.length > 50 ? '...' : '');
                    const type = msg.is_assessment ? 'Assessment Data' : 'Chat Message';
                    html += `<li><strong>${index + 1}.</strong> ${msg.sender_type.toUpperCase()} - ${type}: "${preview}"</li>`;
                });
                
                html += '</ul>';
                analysisDiv.innerHTML = html;
            } catch (err) {
                analysisDiv.innerHTML = `<span class="error">❌ Exception: ${err.message}</span>`;
            }
        };
        
        // Auto-check auth on load
        window.addEventListener('load', () => {
            checkAuth();
        });
    </script>
</body>
</html>
