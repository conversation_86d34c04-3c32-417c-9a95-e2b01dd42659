import { assessmentService } from './assessmentService';
import {
  Assessment,
  AssessmentQuestion,
  QuickAnswer,
  AssessmentResponse,
  AssessmentScores,
  SixHumanNeedsScores,
  ASSESSMENT_TYPES
} from '../types/assessment';

export interface ChatAssessmentState {
  isActive: boolean;
  assessment: Assessment | null;
  currentQuestionIndex: number;
  questions: AssessmentQuestion[];
  responses: AssessmentResponse[];
  sessionId: string;
  startedAt: Date;
}

export interface ChatAssessmentMessage {
  type: 'assessment_question' | 'assessment_result' | 'assessment_start';
  content: string;
  questionId?: string;
  quickAnswers?: QuickAnswer[];
  assessmentResult?: AssessmentScores;
}

class ChatAssessmentService {
  private static instance: ChatAssessmentService;
  private assessmentStates: Map<string, ChatAssessmentState> = new Map();

  static getInstance(): ChatAssessmentService {
    if (!ChatAssessmentService.instance) {
      ChatAssessmentService.instance = new ChatAssessmentService();
    }
    return ChatAssessmentService.instance;
  }

  /**
   * Start a new assessment in a chat session
   */
  async startAssessment(sessionId: string, assessmentId: string): Promise<ChatAssessmentMessage> {
    try {
      console.log('🚀 [ChatAssessmentService] Starting assessment:', { sessionId, assessmentId });

      // Get assessment details
      const { assessment, questions } = await assessmentService.getAssessmentDetail(assessmentId);

      // Initialize assessment state
      const state: ChatAssessmentState = {
        isActive: true,
        assessment,
        currentQuestionIndex: 0,
        questions,
        responses: [],
        sessionId,
        startedAt: new Date()
      };

      this.assessmentStates.set(sessionId, state);

      // Generate welcome message
      const welcomeMessage = this.generateWelcomeMessage(assessment);
      
      return {
        type: 'assessment_start',
        content: welcomeMessage
      };
    } catch (error) {
      console.error('❌ [ChatAssessmentService] Error starting assessment:', error);
      throw error;
    }
  }

  /**
   * Get the next question for the assessment
   */
  getNextQuestion(sessionId: string): ChatAssessmentMessage | null {
    const state = this.assessmentStates.get(sessionId);
    if (!state || !state.isActive) {
      return null;
    }

    if (state.currentQuestionIndex >= state.questions.length) {
      // Assessment completed
      return this.completeAssessment(sessionId);
    }

    const question = state.questions[state.currentQuestionIndex];
    const questionMessage = this.generateQuestionMessage(question, state.currentQuestionIndex + 1, state.questions.length);

    return {
      type: 'assessment_question',
      content: questionMessage,
      questionId: question.id,
      quickAnswers: question.quick_answers
    };
  }

  /**
   * Process user response to an assessment question
   */
  async processResponse(
    sessionId: string, 
    questionId: string, 
    quickAnswerId?: string, 
    freeTextResponse?: string
  ): Promise<boolean> {
    const state = this.assessmentStates.get(sessionId);
    if (!state || !state.isActive) {
      return false;
    }

    try {
      // Find the question and quick answer
      const question = state.questions.find(q => q.id === questionId);
      const quickAnswer = question?.quick_answers?.find(qa => qa.id === quickAnswerId);
      
      if (!question) {
        console.error('❌ [ChatAssessmentService] Question not found:', questionId);
        return false;
      }

      // Create response record
      const response: AssessmentResponse = {
        question_id: questionId,
        quick_answer_id: quickAnswerId,
        free_text_response: freeTextResponse,
        score: quickAnswer?.score_value || 0,
        timestamp: new Date()
      };

      // Save response to database
      await assessmentService.saveAssessmentResponse(
        sessionId,
        questionId,
        quickAnswerId,
        freeTextResponse,
        response.score
      );

      // Add to local state
      state.responses.push(response);
      state.currentQuestionIndex++;

      console.log('✅ [ChatAssessmentService] Response processed:', {
        questionIndex: state.currentQuestionIndex - 1,
        totalQuestions: state.questions.length,
        score: response.score
      });

      return true;
    } catch (error) {
      console.error('❌ [ChatAssessmentService] Error processing response:', error);
      return false;
    }
  }

  /**
   * Complete the assessment and calculate results
   */
  private completeAssessment(sessionId: string): ChatAssessmentMessage {
    const state = this.assessmentStates.get(sessionId);
    if (!state) {
      throw new Error('Assessment state not found');
    }

    try {
      // Calculate scores based on assessment type
      let scores: AssessmentScores;

      if (state.assessment?.type === ASSESSMENT_TYPES.SIX_HUMAN_NEEDS) {
        const sixHNScores = assessmentService.calculate6HNScores(state.responses);
        const insights = assessmentService.generateAssessmentInsights(sixHNScores);
        
        scores = {
          type: '6HN',
          scores: sixHNScores,
          insights,
          dominant_traits: this.getDominantTraits(sixHNScores),
          completion_percentage: (state.responses.length / state.questions.length) * 100
        };
      } else {
        scores = {
          type: state.assessment?.type as any,
          scores: {},
          insights: [],
          dominant_traits: [],
          completion_percentage: (state.responses.length / state.questions.length) * 100
        };
      }

      // Save results to database
      assessmentService.saveAssessmentResult(sessionId, state.assessment!.id, scores);

      // Mark assessment as completed
      state.isActive = false;

      // Generate results message
      const resultsMessage = this.generateResultsMessage(scores);

      return {
        type: 'assessment_result',
        content: resultsMessage,
        assessmentResult: scores
      };
    } catch (error) {
      console.error('❌ [ChatAssessmentService] Error completing assessment:', error);
      throw error;
    }
  }

  /**
   * Check if a session has an active assessment
   */
  hasActiveAssessment(sessionId: string): boolean {
    const state = this.assessmentStates.get(sessionId);
    return state?.isActive || false;
  }

  /**
   * Get assessment progress
   */
  getAssessmentProgress(sessionId: string): { current: number; total: number; percentage: number } | null {
    const state = this.assessmentStates.get(sessionId);
    if (!state) return null;

    return {
      current: state.currentQuestionIndex,
      total: state.questions.length,
      percentage: (state.currentQuestionIndex / state.questions.length) * 100
    };
  }

  /**
   * Cancel an active assessment
   */
  cancelAssessment(sessionId: string): void {
    const state = this.assessmentStates.get(sessionId);
    if (state) {
      state.isActive = false;
    }
  }

  // Helper methods for generating messages
  private generateWelcomeMessage(assessment: Assessment): string {
    return `Great! Let's start your ${assessment.title}. ${assessment.description}\n\nI'll ask you a series of questions to understand your personality better. For each question, you can either choose from the quick response options or share your own thoughts. Ready to begin?`;
  }

  private generateQuestionMessage(question: AssessmentQuestion, current: number, total: number): string {
    const progressText = `**Question ${current} of ${total}** (${question.category})\n\n`;
    const questionText = question.question_text;
    const followUpText = question.follow_up_text ? `\n\n*${question.follow_up_text}*` : '';
    
    return `${progressText}${questionText}${followUpText}`;
  }

  private generateResultsMessage(scores: AssessmentScores): string {
    if (scores.type === '6HN') {
      const sixHNScores = scores.scores as SixHumanNeedsScores;
      const topTraits = scores.dominant_traits.slice(0, 2);
      
      let message = `🎉 **Assessment Complete!**\n\n`;
      message += `Your completion rate: ${scores.completion_percentage.toFixed(0)}%\n\n`;
      message += `**Your Dominant Traits:**\n`;
      topTraits.forEach((trait, index) => {
        message += `${index + 1}. ${trait}\n`;
      });
      
      message += `\n**Your Six Human Needs Scores:**\n`;
      message += `• Certainty: ${sixHNScores.certainty.toFixed(1)}/10\n`;
      message += `• Uncertainty/Variety: ${sixHNScores.uncertainty_variety.toFixed(1)}/10\n`;
      message += `• Significance: ${sixHNScores.significance.toFixed(1)}/10\n`;
      message += `• Connection/Love: ${sixHNScores.connection_love.toFixed(1)}/10\n`;
      message += `• Growth: ${sixHNScores.growth.toFixed(1)}/10\n`;
      message += `• Contribution: ${sixHNScores.contribution.toFixed(1)}/10\n`;
      
      if (scores.insights.length > 0) {
        message += `\n**Key Insights:**\n`;
        scores.insights.slice(0, 3).forEach((insight, index) => {
          message += `${index + 1}. **${insight.trait}**: ${insight.description}\n`;
        });
      }
      
      message += `\nWould you like me to explain any of these results in more detail or discuss how you can use this information for personal growth?`;
      
      return message;
    }
    
    return `🎉 **Assessment Complete!** Your results have been calculated and saved.`;
  }

  private getDominantTraits(scores: SixHumanNeedsScores): string[] {
    const traits = [
      { name: 'Certainty', score: scores.certainty },
      { name: 'Uncertainty/Variety', score: scores.uncertainty_variety },
      { name: 'Significance', score: scores.significance },
      { name: 'Connection/Love', score: scores.connection_love },
      { name: 'Growth', score: scores.growth },
      { name: 'Contribution', score: scores.contribution }
    ];

    return traits
      .sort((a, b) => b.score - a.score)
      .slice(0, 2)
      .map(trait => trait.name);
  }
}

export const chatAssessmentService = ChatAssessmentService.getInstance();
