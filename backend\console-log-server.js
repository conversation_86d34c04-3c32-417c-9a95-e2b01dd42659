/**
 * Auxiliary Debug Log Server
 * This script is a temporary tool for development/debugging only.
 * It should not be imported, required, or referenced by the main codebase.
 *
 * To avoid interference, do not add this file to production builds or main app imports.
 */

const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');
const app = express();
const PORT = process.env.PORT || 3001;

// Use a secret token to prevent accidental log clearing from main app or other sources
const CLEAR_LOG_TOKEN = process.env.CLEAR_LOG_TOKEN || 'dev-debug-token';

app.use(cors());
app.use(express.json());

const LOG_FILE = path.join(__dirname, 'console-debug.log');

// Truncate the log file on server start to keep only the current session's output
fs.writeFileSync(LOG_FILE, '', { flag: 'w' });

app.post('/api/log', (req, res) => {
  const { type, message, timestamp } = req.body;
  const logLine = `[${timestamp}] [${type}] ${message}\n`;
  // Append log entry to the end of the file
  fs.appendFile(LOG_FILE, logLine, (err) => {
    if (err) {
      console.error('Failed to write log:', err);
      return res.status(500).send('Failed to write log');
    }
    res.status(200).send('Logged');
  });
});

// Endpoint to clear the log file (should be called from frontend on reload, with token)
app.post('/api/clear-log', (req, res) => {
  const { token } = req.body;
  if (token !== CLEAR_LOG_TOKEN) {
    return res.status(403).send('Forbidden');
  }
  fs.writeFile(LOG_FILE, '', (err) => {
    if (err) {
      console.error('Failed to clear log:', err);
      return res.status(500).send('Failed to clear log');
    }
    res.status(200).send('Log cleared');
  });
});

app.listen(PORT, () => {
  console.log(`Debug log server running on port ${PORT}`);
});
