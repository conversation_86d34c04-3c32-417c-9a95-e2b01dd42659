interface AssistantsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: (assistant: Assistant) => void;
  onAdd: () => void;
  isAdmin?: boolean;
}

const truncate = (str: string, max: number) =>
  str.length > max ? str.slice(0, max - 1) + "…" : str;

const AssistantsModal: React.FC<AssistantsModalProps> = ({
  isOpen,
  onClose,
  onEdit,
  onAdd,
  isAdmin = false,
}) => {
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadAssistants = async () => {
    setIsLoading(true);
    try {
      const fetchedAssistants = await assistantService.getAssistants();
      setAssistants(fetchedAssistants);
    } catch (error) {
      console.error("Error loading assistants:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      loadAssistants();
    }
  }, [isOpen]);

  const handleDelete = async (id: string) => {
    try {
      await assistantService.deleteAssistant(id);
      await loadAssistants(); // Reload the list
      setDeleteId(null);
    } catch (error) {
      console.error("Error deleting assistant:", error);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/30"
      data-oid="ezrj-2_"
    >
      <div
        className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl w-full max-w-3xl p-8 relative"
        data-oid="lvvt7ci"
      >
        <button
          className="absolute top-4 right-4 text-slate-400 hover:text-slate-700 dark:hover:text-white"
          onClick={onClose}
          aria-label="Close"
          data-oid="hdb3td8"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            data-oid="mzaaq:."
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6 18L18 6M6 6l12 12"
              data-oid="4l.vxxu"
            />
          </svg>
        </button>
        <h2
          className="text-xl font-bold mb-6 text-slate-800 dark:text-white"
          data-oid="7iv_rl2"
        >
          Available Assistants
        </h2>
        <p
          className="text-sm text-slate-600 dark:text-slate-400 mb-6"
          data-oid="l3e:fdd"
        >
          These assistants are available to all users. Each has specialized
          knowledge and personality traits.
        </p>

        {isLoading ? (
          <div
            className="flex items-center justify-center py-8"
            data-oid="x1l0a9-"
          >
            <div
              className="text-slate-600 dark:text-slate-300"
              data-oid="bddv-xp"
            >
              Loading assistants...
            </div>
          </div>
        ) : (
          <div
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-slate-400 scrollbar-track-slate-100 dark:scrollbar-thumb-slate-600 dark:scrollbar-track-slate-800"
            data-oid="60hmnym"
          >
            {assistants.map((a) => (
              <div
                key={a.id}
                className="bg-slate-50 dark:bg-slate-700 rounded-xl p-4 flex flex-col shadow border border-slate-200 dark:border-slate-600 relative"
                data-oid="x:npffk"
              >
                <div className="flex items-center mb-2" data-oid="38.69-6">
                  <span className="text-2xl mr-2" data-oid="vi8ny87">
                    {a.emoji}
                  </span>
                  <span
                    className="font-semibold text-slate-800 dark:text-white flex-1 truncate"
                    data-oid="mu:ikkf"
                  >
                    {a.name}
                  </span>
                  {isAdmin && (
                    <div className="flex gap-2 ml-2" data-oid="475m.en">
                      <button
                        title="Edit"
                        className="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600"
                        onClick={() => onEdit(a)}
                        data-oid="7:oun9d"
                      >
                        <EditMessageIcon
                          className="w-4 h-4 text-blue-600 dark:text-blue-400"
                          data-oid="6lm.d-:"
                        />
                      </button>
                      <button
                        title="Delete"
                        className="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600"
                        onClick={() => setDeleteId(a.id)}
                        data-oid="6p0hlhd"
                      >
                        <DeleteMessageIcon
                          className="w-4 h-4 text-red-500 dark:text-red-400"
                          data-oid="w.7baod"
                        />
                      </button>
                    </div>
                  )}
                </div>
                <div
                  className="text-slate-600 dark:text-slate-300 text-sm line-clamp-2 min-h-[2.5em]"
                  data-oid="ax4py15"
                >
                  {truncate(a.description, 80)}
                </div>
                {/* Confirm delete modal (simple inline) */}
                {isAdmin && deleteId === a.id && (
                  <div
                    className="absolute inset-0 bg-black/40 flex items-center justify-center z-10 rounded-xl"
                    data-oid="7-wqj_."
                  >
                    <div
                      className="bg-white dark:bg-slate-800 p-4 rounded shadow-xl flex flex-col items-center"
                      data-oid="t5iswep"
                    >
                      <div
                        className="mb-2 text-slate-800 dark:text-white font-semibold"
                        data-oid="m8po9zo"
                      >
                        Delete assistant?
                      </div>
                      <div
                        className="mb-4 text-xs text-slate-500 dark:text-slate-400"
                        data-oid="uydzvru"
                      >
                        This action cannot be undone.
                      </div>
                      <div className="flex gap-2" data-oid="4yin9_y">
                        <button
                          className="px-3 py-1 rounded bg-red-500 text-white"
                          onClick={() => handleDelete(a.id)}
                          data-oid="0_72b7e"
                        >
                          Delete
                        </button>
                        <button
                          className="px-3 py-1 rounded bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-200"
                          onClick={() => setDeleteId(null)}
                          data-oid="_wcj0rs"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
            {/* Admin-only: Create New Assistant card */}
            {isAdmin ? (
              <div
                className="flex flex-col items-center justify-center border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-xl p-4 min-h-[120px] bg-slate-50 dark:bg-slate-800 cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700"
                onClick={onAdd}
                data-oid="9nd830u"
              >
                <svg
                  className="w-8 h-8 text-blue-400 mb-2"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  data-oid="5_uu0ui"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 4v16m8-8H4"
                    data-oid="09s1c0_"
                  />
                </svg>
                <span
                  className="text-blue-600 dark:text-blue-300 text-sm text-center font-semibold"
                  data-oid="2_l3_pi"
                >
                  Create New Assistant
                </span>
              </div>
            ) : (
              <div
                className="flex flex-col items-center justify-center border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-xl p-4 min-h-[120px] bg-slate-50 dark:bg-slate-800"
                data-oid="_04rhvc"
              >
                <svg
                  className="w-8 h-8 text-slate-400 mb-2"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  data-oid="t6m2817"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    data-oid="61:r5m8"
                  />
                </svg>
                <span
                  className="text-slate-500 dark:text-slate-300 text-sm text-center"
                  data-oid="do7y7lo"
                >
                  Assistants are managed globally
                  <br data-oid="h-tdqjy" />
                  <span className="text-xs" data-oid="iof39a0">
                    Available to all users
                  </span>
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AssistantsModal;
import React, { useEffect, useState } from "react";
import type { Assistant } from "../types/assistant";
import { assistantService } from "../services/assistantService";
import { EditMessageIcon, DeleteMessageIcon } from "../constants";
