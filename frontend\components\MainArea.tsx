import React, { useState, useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Message, Model, User } from "../types";
import {
  MODELS,
  ChevronDownIcon,
  GithubIcon,
  TwitterXIcon,
  DiscordIcon,
  BrainIcon,
  GEMINI_MODEL_NAME,
  SidebarToggleIcon,
  PencilIcon,
  ZLogoIcon,
} from "../constants";
import ChatInput from "./ChatInput";
import MessageDisplay from "./MessageDisplay";
import UserProfileMenu from "./UserProfileMenu";
import { PASSelector } from "./PASSelector";
import { chatAssessmentService } from "../services/chatAssessmentService";
import { assessmentService } from "../services/assessmentService";
import { assistantService } from "../services/assistantService";
import { dataService } from "../services/dataService";
import { resetChatSession } from "../services/unifiedStreamingService";
import { unifiedStreamingService } from "../services/unifiedStreamingService";
import { View } from "../App";

interface MainHeaderProps {
  selectedModel: Model;
  onModelChange: (modelId: string) => void;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
  onNewChat: () => void;
  navigateTo: (view: View) => void;
  isAuthenticated: boolean;
  currentUser: User;
  handleLogout: () => void;
  openSignInModal: () => void;
}

const ModelDropdown: React.FC<{
  selectedModel: Model;
  onModelChange: (modelId: string) => void;
}> = ({ selectedModel, onModelChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative min-w-0" ref={dropdownRef} data-oid="r.5:lyc">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center text-slate-800 dark:text-slate-200 hover:text-slate-900 dark:hover:text-slate-100 focus:outline-none"
        id="model-dropdown-button"
        aria-haspopup="true"
        aria-expanded={isOpen}
        data-oid="-fh80hs"
      >
        <span className="text-md font-semibold truncate" data-oid="guqbhym">
          <span className="hidden sm:inline" data-oid="c.j3:v2">
            {selectedModel.name}
          </span>
          <span className="sm:hidden" data-oid="g5_r2e4">
            {selectedModel.name.split(" ")[0]}
          </span>
        </span>
        <ChevronDownIcon
          className={`w-5 h-5 ml-1 flex-shrink-0 transition-transform transform ${isOpen ? "rotate-180" : "rotate-0"}`}
          data-oid="zn5s8.:"
        />
      </button>
      {isOpen && (
        <div
          className="absolute left-0 mt-2 w-56 bg-white dark:bg-slate-700 rounded-md shadow-lg ring-1 ring-black dark:ring-slate-600 ring-opacity-5 z-20 py-1"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="model-dropdown-button"
          data-oid=":yfp66q"
        >
          {MODELS.map((model) => (
            <button
              key={model.id}
              onClick={() => {
                onModelChange(model.id);
                setIsOpen(false);
              }}
              className="block w-full text-left px-4 py-2 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600"
              role="menuitem"
              data-oid="bkn2n9."
            >
              {model.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

const MainHeader: React.FC<MainHeaderProps> = ({
  selectedModel,
  onModelChange,
  isSidebarOpen,
  onToggleSidebar,
  onNewChat,
  navigateTo,
  isAuthenticated,
  currentUser,
  handleLogout,
  openSignInModal,
}) => {
  const { t } = useTranslation();
  const [isHeaderUserMenuOpen, setIsHeaderUserMenuOpen] = useState(false);
  const headerAvatarRef = useRef<HTMLButtonElement>(null);
  const headerUserMenuButtonId = "header-user-menu-button";

  const toggleHeaderUserMenu = () => {
    setIsHeaderUserMenuOpen((prev) => !prev);
  };

  return (
    <>
      <div
        className="flex items-center justify-between px-2 sm:px-3 md:px-5 h-12 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 flex-shrink-0 min-w-0"
        data-oid="gir2f3m"
      >
        <div
          className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1"
          data-oid="jlo.c2h"
        >
          {(!isSidebarOpen || !isAuthenticated) && (
            <div className="flex items-center space-x-1" data-oid="i.kinme">
              <button
                onClick={onToggleSidebar}
                className="p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md"
                aria-label={
                  isAuthenticated
                    ? isSidebarOpen
                      ? "Collapse sidebar"
                      : "Expand sidebar"
                    : t("chatHistoryAccessTitle")
                }
                data-oid="00t1ovf"
              >
                <SidebarToggleIcon
                  className={`w-5 h-5 transition-transform duration-300 transform ${isSidebarOpen && isAuthenticated ? "rotate-180" : "rotate-0"}`}
                  data-oid="qx_gjls"
                />
              </button>
              <button
                onClick={onNewChat}
                className="p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md"
                aria-label={t("newChat")}
                data-oid="3971bx0"
              >
                <PencilIcon className="w-5 h-5" data-oid=":pjgw4h" />
              </button>
            </div>
          )}
          <div className="min-w-0 flex-shrink" data-oid="7cpr9hx">
            <ModelDropdown
              selectedModel={selectedModel}
              onModelChange={onModelChange}
              data-oid="3q_i7fd"
            />
          </div>
        </div>
        {isAuthenticated && currentUser && currentUser.name ? (
          <button
            ref={headerAvatarRef}
            onClick={toggleHeaderUserMenu}
            className="rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-800 flex-shrink-0"
            id={headerUserMenuButtonId}
            aria-haspopup="true"
            aria-expanded={isHeaderUserMenuOpen}
            aria-label="User menu"
            data-oid="cbfwk0r"
          >
            {currentUser.avatarUrl ? (
              <img
                src={currentUser.avatarUrl}
                alt={currentUser.name}
                className="w-8 h-8 sm:w-9 sm:h-9 rounded-full"
                data-oid="d2rika2"
              />
            ) : (
              <div
                className="w-8 h-8 sm:w-9 sm:h-9 rounded-full bg-slate-300 dark:bg-slate-600 flex items-center justify-center text-slate-500 dark:text-slate-300 font-semibold text-sm"
                data-oid="0dztltv"
              >
                {currentUser.name.substring(0, 1).toUpperCase()}
              </div>
            )}
          </button>
        ) : !isAuthenticated ? (
          <button
            onClick={openSignInModal}
            className="px-3 sm:px-4 py-2 bg-[#222428] dark:bg-slate-700 text-white text-sm font-medium rounded-lg hover:bg-opacity-90 dark:hover:bg-slate-600 transition-colors flex-shrink-0"
            data-oid="oi6cix:"
          >
            <span className="hidden sm:inline" data-oid="1cm93sp">
              {t("signIn")}
            </span>
            <span className="sm:hidden" data-oid="zk8i7jf">
              Sign In
            </span>
          </button>
        ) : null}
      </div>
      {isAuthenticated && isHeaderUserMenuOpen && (
        <UserProfileMenu
          isOpen={isHeaderUserMenuOpen}
          onClose={() => setIsHeaderUserMenuOpen(false)}
          triggerRef={headerAvatarRef}
          menuPosition="bottom-right"
          triggerButtonId={headerUserMenuButtonId}
          navigateTo={navigateTo}
          handleLogout={handleLogout}
          data-oid="_uj_n_k"
        />
      )}
    </>
  );
};

const WelcomeScreen: React.FC<{
  onSendMessage: (message: string, useWebSearch: boolean) => void;
  isLoading: boolean;
  isAuthenticated: boolean;
  currentUser: User;
  selectedAssistant?: { name: string; emoji: string } | null;
  onSelectAssessment: (assessmentId: string) => void;
}> = ({
  onSendMessage,
  isLoading,
  isAuthenticated,
  currentUser,
  selectedAssistant,
  onSelectAssessment,
}) => {
  const { t } = useTranslation();

  let userName = "";
  let assistantTitle = "";
  if (isAuthenticated && currentUser.name) {
    userName = currentUser.name.split("(")[0].trim();
    if (selectedAssistant) {
      assistantTitle = selectedAssistant.name;
    }
  }

  return (
    <div
      className="flex-1 flex flex-col items-center justify-center p-3 text-center"
      data-oid="bf_5sfw"
    >
      {/* PASSelector always visible */}
      <PASSelector onSelectAssessment={onSelectAssessment} data-oid="5nyg8x:" />
      {!isAuthenticated && (
        <ZLogoIcon
          className="w-16 h-16 text-slate-700 dark:text-slate-300 mb-6"
          data-oid="4n5hts2"
        />
      )}
      <h1
        className="text-3xl sm:text-4xl font-semibold text-slate-800 dark:text-slate-100 mt-4 mb-4"
        data-oid="x7bisto"
      >
        {isAuthenticated && userName
          ? `Welcome, ${userName}!`
          : t("welcomeMessageGuest")}
      </h1>
      {isAuthenticated && assistantTitle && (
        <p
          className="text-lg text-slate-700 dark:text-slate-200 mb-8"
          data-oid="6hwa0t:"
        >
          {`I'm your ${assistantTitle} Assistant.`}
        </p>
      )}
      <ChatInput
        onSendMessage={onSendMessage}
        isLoading={isLoading}
        showSuggestions={true}
        data-oid="sqql5cq"
      />
    </div>
  );
};

const Footer: React.FC<{ isAuthenticated: boolean }> = ({
  isAuthenticated,
}) => {
  const { t } = useTranslation();
  return (
    <div
      className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-5 p-4 text-slate-500 dark:text-slate-400 bg-slate-50 dark:bg-slate-800 flex-shrink-0"
      data-oid="l9ha9fm"
    >
      {!isAuthenticated && (
        <div className="text-xs text-center sm:text-left" data-oid="jmcg958">
          <span data-oid="9ugdbj_">{t("footerGuestText")}</span>
          <span className="hidden sm:inline mx-2" data-oid="aoeqbe4">
            |
          </span>
        </div>
      )}
      <div className="flex items-center space-x-5" data-oid="j_krfgi">
        <a
          href="https://github.com/srgryzhkov/gemini-ai-chat-interface"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Github"
          data-oid="xum-xny"
        >
          <GithubIcon data-oid="3kcys6w" />
        </a>
        <a
          href="https://ai.google.dev/gemini-api/docs/models/gemini-2.5-flash"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="AI Model Info"
          data-oid="i:ip.ff"
        >
          <BrainIcon data-oid="9ps3t2s" />
        </a>
        <a
          href="https://x.com/srgryzhkov"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Twitter / X"
          data-oid="hb38t8j"
        >
          <TwitterXIcon data-oid="zhns6ys" />
        </a>
        <a
          href="https://discordapp.com/users/358585938432327680"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Discord"
          data-oid="y34_o_j"
        >
          <DiscordIcon data-oid="k0ndn_." />
        </a>
      </div>
    </div>
  );
};

interface MainAreaProps {
  activeChatId: string | null;
  setActiveChatId: (id: string | null) => void;
  chatMessages: Message[];
  onUpdateChat: (
    chatId: string,
    messages: Message[],
    details?: {
      isNew?: boolean;
      title?: string;
      skipSave?: boolean;
      assistantId?: string | null;
      assistantName?: string | null;
      assistantEmoji?: string | null;
    },
  ) => void;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
  onNewChat: () => void;
  navigateTo: (view: View) => void;
  isAuthenticated: boolean;
  currentUser: User;
  handleLogout: () => void;
  openSignInModal: () => void;
  selectedAssistantId: string | null;
}

const MainArea: React.FC<MainAreaProps> = ({
  activeChatId,
  setActiveChatId,
  chatMessages,
  onUpdateChat,
  isSidebarOpen,
  onToggleSidebar,
  onNewChat,
  navigateTo,
  isAuthenticated,
  currentUser,
  handleLogout,
  openSignInModal,
  selectedAssistantId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<Model>(
    MODELS.find((m) => m.id === GEMINI_MODEL_NAME) || MODELS[0],
  );
  const [selectedAssistant, setSelectedAssistant] = useState<{
    name: string;
    emoji: string;
  } | null>(null);

  // Assessment preview state
  const [assessmentPreview, setAssessmentPreview] = useState<{
    isActive: boolean;
    assessmentId: string;
    assessmentTitle: string;
    assessmentDescription: string;
  } | null>(null);

  // Handler for PAS assessment selection - now shows preview/confirmation
  const handleSelectAssessment = async (assessmentId: string) => {
    if (!isAuthenticated || !currentUser) {
      console.log("User must be authenticated to take assessments");
      return;
    }

    try {
      // Fetch assessment details for preview
      const { assessment } =
        await assessmentService.getAssessmentDetail(assessmentId);

      // Set preview state instead of creating chat session
      setAssessmentPreview({
        isActive: true,
        assessmentId: assessmentId,
        assessmentTitle: assessment.title,
        assessmentDescription: assessment.description,
      });

      // Create a temporary preview message without saving to database
      const userName = currentUser.name || "Guest";
      const welcomeText = `Hello, ${userName}! Now we can discover your ${assessment.title} and how they shape your life decisions and behaviors. This is how we will proceed: I'll ask you a series of questions about your preferences and behaviors, and you can respond with quick answers or share your own thoughts. The assessment takes about 5-10 minutes to complete. Are you ready?`;

      const previewMessageId = `preview-${Date.now()}`;
      const previewMessage = {
        id: previewMessageId,
        text: welcomeText,
        sender: "ai" as const,
        timestamp: new Date(),
        chatId: "preview",
        isLoading: false,
        isError: false,
        avatarUrl: null,
        groundingChunks: [],
        assessmentData: {
          type: "assessment_confirmation" as const,
          assessmentId: assessmentId,
          questionId: "confirmation",
          quickAnswers: [
            { id: "yes", answer_text: "Yes", score_value: 1, order: 1 },
            { id: "no", answer_text: "No", score_value: 0, order: 2 },
          ],
        },
      };

      // Set a temporary chat ID to show the preview
      setActiveChatId("preview");

      // Update chat with preview message (not saved to database)
      onUpdateChat("preview", [previewMessage], {
        skipSave: true,
        isNew: true,
        title: `Assessment Preview - ${assessment.title}`,
      });
    } catch (error) {
      console.error("Error loading assessment preview:", error);
      // If assessment fetch fails, show an error message
      alert(
        `Failed to load assessment details. Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  };

  // Handle assessment confirmation (Yes/No) and regular assessment answers
  const handleAssessmentAnswer = async (
    questionId: string,
    quickAnswerId: string,
    answerText: string,
  ) => {
    console.log("🎯 [handleAssessmentAnswer] Called with:", {
      questionId,
      quickAnswerId,
      answerText,
      activeChatId,
    });

    if (!activeChatId) {
      console.log("❌ [handleAssessmentAnswer] No active chat ID");
      return;
    }

    try {
      // Check if this is a confirmation response (preview mode)
      if (activeChatId === "preview" && assessmentPreview?.isActive) {
        console.log(
          "🔄 [handleAssessmentAnswer] Handling confirmation response...",
        );
        await handleAssessmentConfirmation(quickAnswerId, answerText);
        return;
      }

      // Regular assessment answer handling
      // Add user's response to the chat
      const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const userMessage = {
        id: userMessageId,
        text: answerText,
        sender: "user" as const,
        timestamp: new Date(),
        chatId: activeChatId,
        isLoading: false,
        isError: false,
        avatarUrl: null,
        groundingChunks: [],
      };

      // Update chat with user's response
      const currentMessages = chatMessages || [];
      const messagesWithUserResponse = [...currentMessages, userMessage];
      onUpdateChat(activeChatId, messagesWithUserResponse);

      // Process the response through the chat assessment service
      const success = await chatAssessmentService.processResponse(
        activeChatId,
        questionId,
        quickAnswerId,
        undefined,
      );

      if (success) {
        // Get the next question or completion message
        const nextMessage = chatAssessmentService.getNextQuestion(activeChatId);

        if (nextMessage) {
          // Add a small delay for better UX
          setTimeout(() => {
            const aiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
            const aiMessage = {
              id: aiMessageId,
              text: nextMessage.content,
              sender: "ai" as const,
              timestamp: new Date(),
              chatId: activeChatId,
              isLoading: false,
              isError: false,
              avatarUrl: null,
              groundingChunks: [],
              assessmentData:
                nextMessage.type === "assessment_question"
                  ? {
                      type: nextMessage.type,
                      questionId: nextMessage.questionId,
                      quickAnswers: nextMessage.quickAnswers,
                    }
                  : nextMessage.type === "assessment_result"
                    ? {
                        type: nextMessage.type,
                        assessmentResult: nextMessage.assessmentResult,
                      }
                    : undefined,
            };

            const finalMessages = [...messagesWithUserResponse, aiMessage];
            onUpdateChat(activeChatId, finalMessages);
          }, 800);
        }
      }
    } catch (error) {
      console.error("Error handling assessment answer:", error);
    }
  };

  // Handle assessment confirmation (Yes/No responses in preview mode)
  const handleAssessmentConfirmation = async (
    responseId: string,
    answerText: string,
  ) => {
    console.log("🎯 [handleAssessmentConfirmation] Called with:", {
      responseId,
      answerText,
      assessmentPreview,
    });

    if (!assessmentPreview?.isActive) {
      console.log(
        "❌ [handleAssessmentConfirmation] No active assessment preview",
      );
      return;
    }

    try {
      if (responseId === "no") {
        console.log(
          "❌ [handleAssessmentConfirmation] User declined assessment",
        );
        // User declined - clear preview state and return to welcome screen
        setAssessmentPreview(null);
        setActiveChatId(null);
        return;
      }

      if (responseId === "yes") {
        console.log(
          "✅ [handleAssessmentConfirmation] User confirmed assessment",
        );
        console.log(
          "🔄 [handleAssessmentConfirmation] Creating chat session...",
        );

        // User confirmed - create actual chat session and start assessment
        const newSession = await dataService.createChatSession(
          `Personality Assessment - ${assessmentPreview.assessmentTitle}`,
          "SpeechBubbleIcon",
          selectedAssistantId || null,
          selectedAssistant?.name || null,
          selectedAssistant?.emoji || null,
        );

        console.log(
          "✅ [handleAssessmentConfirmation] Chat session created:",
          newSession,
        );

        if (newSession && newSession.id) {
          // Set the real chat session
          setActiveChatId(newSession.id);

          // Add user's confirmation response to the real chat
          const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
          const userMessage = {
            id: userMessageId,
            text: answerText,
            sender: "user" as const,
            timestamp: new Date(),
            chatId: newSession.id,
            isLoading: false,
            isError: false,
            avatarUrl: null,
            groundingChunks: [],
          };

          // Start the actual assessment
          const welcomeMessage = await chatAssessmentService.startAssessment(
            newSession.id,
            assessmentPreview.assessmentId,
          );

          const aiWelcomeId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
          const aiWelcomeMessage = {
            id: aiWelcomeId,
            text: welcomeMessage.content,
            sender: "ai" as const,
            timestamp: new Date(),
            chatId: newSession.id,
            isLoading: false,
            isError: false,
            avatarUrl: null,
            groundingChunks: [],
          };

          // Get the first question
          const firstQuestion = chatAssessmentService.getNextQuestion(
            newSession.id,
          );

          let initialMessages = [userMessage, aiWelcomeMessage];

          if (firstQuestion) {
            const questionMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
            const questionMessage = {
              id: questionMessageId,
              text: firstQuestion.content,
              sender: "ai" as const,
              timestamp: new Date(),
              chatId: newSession.id,
              isLoading: false,
              isError: false,
              avatarUrl: null,
              groundingChunks: [],
              assessmentData: {
                type: "assessment_question" as const,
                questionId: firstQuestion.questionId,
                quickAnswers: firstQuestion.quickAnswers,
              },
            };

            initialMessages.push(questionMessage);
          }

          // Update the chat with all messages
          onUpdateChat(newSession.id, initialMessages, {
            isNew: true,
            title: `Personality Assessment - ${assessmentPreview.assessmentTitle}`,
            assistantId: selectedAssistantId,
            assistantName: selectedAssistant?.name,
            assistantEmoji: selectedAssistant?.emoji,
          });

          // Clear preview state
          setAssessmentPreview(null);
        } else {
          console.error("Failed to create chat session for assessment");
        }
      }
    } catch (error) {
      console.error("Error handling assessment confirmation:", error);
    }
  };

  // Refs for scrolling behavior
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const lastMessageCountRef = useRef(0);
  // Removed unused refs - using simpler approach without intervals

  // Always use chatMessages prop as the source of truth, but handle preview mode
  // Note: chatMessages is an array of messages for the current chat, not an object
  const messages = chatMessages || [];

  // Debug: Log when messages change
  useEffect(() => {
    console.log(`📨 [MainArea] Messages updated: ${messages.length} messages`);
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      console.log(
        `📨 [MainArea] Last message: ${lastMessage.sender} - "${lastMessage.text?.substring(0, 50)}..." (loading: ${lastMessage.isLoading})`,
      );
    }
  }, [messages]);

  // Debug streaming updates to see if text is changing
  useEffect(() => {
    const streamingMessage = messages.find((m) => m.isLoading);
    if (streamingMessage) {
      console.log(
        "🔄 [MainArea] Streaming message text length:",
        streamingMessage.text?.length,
        "Text preview:",
        streamingMessage.text?.substring(0, 50) + "...",
      );
    }
  }, [messages]);

  // Fetch assistant information when selectedAssistantId changes
  useEffect(() => {
    const fetchAssistant = async () => {
      if (selectedAssistantId) {
        try {
          console.log(
            "[Assistant Debug] Fetching assistant with ID:",
            selectedAssistantId,
          );
          const assistant =
            await assistantService.getAssistant(selectedAssistantId);
          console.log("[Assistant Debug] Assistant fetched:", assistant);
          if (assistant) {
            setSelectedAssistant({
              name: assistant.name,
              emoji: assistant.emoji,
            });
          } else {
            setSelectedAssistant(null);
          }
        } catch (error) {
          console.error("[Assistant Debug] Error fetching assistant:", error);
          setSelectedAssistant(null);
        }
      } else {
        setSelectedAssistant(null);
      }
    };

    fetchAssistant();
  }, [selectedAssistantId]);

  // Auto-scroll behavior - scroll to bottom when messages change
  useEffect(() => {
    if (!messagesContainerRef.current) {
      console.log("⚠️ [MainArea] messagesContainerRef is null");
      return;
    }

    const container = messagesContainerRef.current;

    // Scroll to bottom when messages change
    const scrollToBottom = () => {
      if (container) {
        const oldScrollTop = container.scrollTop;
        container.scrollTop = container.scrollHeight;
        console.log(
          `📜 [MainArea] Scrolled from ${oldScrollTop} to ${container.scrollTop} (height: ${container.scrollHeight})`,
        );
      }
    };

    // Use requestAnimationFrame for better performance
    requestAnimationFrame(scrollToBottom);
  }, [messages]);

  // Additional scroll effect for streaming messages
  useEffect(() => {
    if (!messagesContainerRef.current) return;

    const container = messagesContainerRef.current;
    const lastMessage = messages[messages.length - 1];

    // If the last message is loading (streaming), keep scrolling
    if (lastMessage?.isLoading) {
      const scrollToBottom = () => {
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      };

      // Scroll immediately and then again after a short delay
      scrollToBottom();
      const timeoutId = setTimeout(scrollToBottom, 50);

      return () => clearTimeout(timeoutId);
    }
  }, [messages, messages[messages.length - 1]?.text]);

  const handleModelChange = (modelId: string) => {
    const newModel = MODELS.find((m) => m.id === modelId);
    if (newModel) {
      setSelectedModel(newModel);
      resetChatSession();
    }
  };

  const generateTitleFromMessage = (text: string): string => {
    return (
      text.split(" ").slice(0, 5).join(" ") +
      (text.split(" ").length > 5 ? "..." : "")
    );
  };

  const handleSendMessage = useCallback(
    async (inputText: string, useWebSearch: boolean) => {
      if (!currentUser) return;

      // Handle assessment preview confirmation via typed message
      if (activeChatId === "preview" && assessmentPreview?.isActive) {
        const lowerInput = inputText.toLowerCase().trim();

        // Check for positive confirmation
        if (
          lowerInput.includes("yes") ||
          lowerInput.includes("sure") ||
          lowerInput.includes("ok") ||
          lowerInput.includes("ready") ||
          lowerInput.includes("start") ||
          lowerInput.includes("begin")
        ) {
          await handleAssessmentConfirmation("yes", inputText);
          return;
        }

        // Check for negative confirmation
        if (
          lowerInput.includes("no") ||
          lowerInput.includes("not") ||
          lowerInput.includes("cancel") ||
          lowerInput.includes("stop") ||
          lowerInput.includes("exit")
        ) {
          await handleAssessmentConfirmation("no", inputText);
          return;
        }

        // For ambiguous responses, treat as confirmation to proceed
        await handleAssessmentConfirmation("yes", inputText);
        return;
      }

      setIsLoading(true);

      const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const aiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const isNewChatSession = !activeChatId;
      let currentChatIdToUse: string = activeChatId || "";

      if (isNewChatSession) {
        if (isAuthenticated) {
          // For registered users, create chat session in Supabase immediately
          const now = new Date();
          console.log(
            "[Assistant Debug] Creating new chat session with assistant context:",
            {
              inputText,
              selectedAssistantId,
              selectedAssistant,
              currentUser,
              isAuthenticated,
            },
          );
          const newSession = await dataService.createChatSession(
            generateTitleFromMessage(inputText),
            "SpeechBubbleIcon",
            selectedAssistantId || null,
            selectedAssistant?.name || null,
            selectedAssistant?.emoji || null,
          );
          console.log(
            "[Assistant Debug] New chat session created:",
            newSession,
          );
          if (newSession && newSession.id) {
            currentChatIdToUse = newSession.id;
            setActiveChatId(newSession.id);
          } else {
            // Fallback to temp ID if creation fails
            currentChatIdToUse = `temp-${Date.now()}`;
            setActiveChatId(currentChatIdToUse);
          }
        } else {
          // For guests, use a temporary ID
          console.log(
            "[Assistant Debug] Creating temp chat session for guest:",
            {
              inputText,
              selectedAssistantId,
              selectedAssistant,
              currentUser,
              isAuthenticated,
            },
          );
          currentChatIdToUse = `temp-${Date.now()}`;
          setActiveChatId(currentChatIdToUse);
        }

        // Ensure currentChatIdToUse is always a string
        if (!currentChatIdToUse) {
          currentChatIdToUse = `temp-${Date.now()}`;
          setActiveChatId(currentChatIdToUse);
        }
      }

      const newUserMessage: Message = {
        id: userMessageId,
        text: inputText,
        sender: "user",
        timestamp: new Date(),
        avatar: currentUser.avatarUrl,
        chatId: currentChatIdToUse,
      };

      const updatedMessagesAfterUser = [...messages, newUserMessage];

      // Update chat with user message first and trigger scroll to user message
      const aiPlaceholderMessage: Message = {
        id: aiMessageId,
        text: "",
        sender: "ai",
        timestamp: new Date(),
        isLoading: true,
        chatId: currentChatIdToUse,
      };
      const updatedMessagesWithPlaceholder = [
        ...updatedMessagesAfterUser,
        aiPlaceholderMessage,
      ];

      // Simple approach: Create chat with user message and AI placeholder
      console.log(
        "🚀 [handleSendMessage] Creating/updating chat with user message and AI placeholder",
      );
      console.log("[Assistant Debug] onUpdateChat called with:", {
        currentChatIdToUse,
        updatedMessagesWithPlaceholder,
        assistantContext: {
          selectedAssistantId,
          selectedAssistant,
        },
      });
      onUpdateChat(currentChatIdToUse, updatedMessagesWithPlaceholder, {
        isNew: isNewChatSession,
        title: isNewChatSession
          ? generateTitleFromMessage(inputText)
          : undefined,
        assistantId: selectedAssistantId,
        assistantName: selectedAssistant?.name || null,
        assistantEmoji: selectedAssistant?.emoji || null,
      });

      let accumulatedAiText = "";
      let finalGroundingChunks: any[] | undefined;
      let finalMessagesForApp: Message[] = updatedMessagesWithPlaceholder;
      let chunkCount = 0;

      try {
        // Get the selected assistant for context
        let assistant = null;
        if (selectedAssistantId && assistantService) {
          try {
            console.log(
              "🔍 [handleSendMessage] Fetching assistant:",
              selectedAssistantId,
            );
            console.log(
              "[Assistant Debug] Fetching assistant for message context:",
              {
                selectedAssistantId,
                selectedAssistant,
              },
            );
            assistant =
              await assistantService.getAssistant(selectedAssistantId);
            console.log(
              "[Assistant Debug] Assistant used for message:",
              assistant,
            );
            console.log(
              "🤖 [handleSendMessage] Retrieved assistant:",
              assistant?.name || "null",
            );
          } catch (error) {
            console.error(
              "❌ [handleSendMessage] Error fetching assistant:",
              error,
            );
            console.error(
              "[Assistant Debug] Error fetching assistant for message:",
              error,
            );
            assistant = null;
          }
        } else {
          console.log(
            "⚠️ [handleSendMessage] No assistant selected or service unavailable",
          );
          console.log(
            "[Assistant Debug] No assistant selected or service unavailable:",
            {
              selectedAssistantId,
              selectedAssistant,
            },
          );
        }

        // Use unified streaming service
        const providerName = selectedModel.id.startsWith("openrouter:")
          ? "openrouter"
          : selectedModel.id.startsWith("mistral:")
            ? "mistral"
            : "gemini";

        // Prepare request with unified format
        const streamRequest = {
          prompt: inputText,
          systemMessage:
            assistant?.instructions ||
            "You are a helpful and friendly AI assistant.",
          model: selectedModel.id
            .replace("openrouter:", "")
            .replace("mistral:", ""),
          temperature: 0.7,
          search: {
            web: useWebSearch,
          },
        };

        // Use unified streaming service
        const stream = unifiedStreamingService.sendStream(
          streamRequest,
          providerName,
        );

        for await (const chunk of stream) {
          if (chunk.error) {
            accumulatedAiText = `Error: ${chunk.error.message}`;
            finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
              msg.id === aiMessageId
                ? {
                    ...msg,
                    text: accumulatedAiText,
                    isLoading: false,
                    isError: true,
                  }
                : msg,
            );
            // Error will be handled in final save below
            break;
          }
          if (chunk.text) {
            accumulatedAiText += chunk.text;
            chunkCount++;
          }
          if (chunk.metadata?.groundingChunks) {
            finalGroundingChunks = chunk.metadata.groundingChunks;
          }
          finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
            msg.id === aiMessageId
              ? {
                  ...msg,
                  text: accumulatedAiText,
                  isLoading: true,
                  groundingChunks: finalGroundingChunks || msg.groundingChunks,
                }
              : msg,
          );

          // Debug: Log the AI message being updated
          if (chunkCount % 10 === 0) {
            const aiMsg = finalMessagesForApp.find((m) => m.id === aiMessageId);
            console.log(
              `📝 [handleSendMessage] AI message text preview: "${aiMsg?.text?.substring(0, 50)}..."`,
            );
          }
          // Update UI more frequently for better streaming experience
          if (
            chunkCount <= 5 ||
            chunkCount % 2 === 0 ||
            chunk.metadata?.groundingChunks
          ) {
            console.log(
              `🔄 [handleSendMessage] UI update #${chunkCount}, text length: ${accumulatedAiText.length}`,
            );
            console.log(
              `🔄 [handleSendMessage] Updating with messages:`,
              finalMessagesForApp.map(
                (m) => `${m.sender}: ${m.text?.substring(0, 30)}...`,
              ),
            );
            onUpdateChat(currentChatIdToUse, finalMessagesForApp, {
              skipSave: true, // Only update UI, don't save to database during streaming
            });
          }
        }

        console.log(
          "🔍 [handleSendMessage] Stream completed, preparing final AI message for saving",
        );

        // Final update: Mark AI response as complete and save
        const finalMessages = finalMessagesForApp.map((msg) =>
          msg.id === aiMessageId
            ? {
                ...msg,
                text: accumulatedAiText,
                isLoading: false,
                groundingChunks: finalGroundingChunks || msg.groundingChunks,
              }
            : msg,
        );

        // Save final state to database
        onUpdateChat(currentChatIdToUse, finalMessages, {
          skipSave: false,
        });
      } catch (error) {
        console.error("Error handling stream:", error);
        const errorText =
          error instanceof Error ? error.message : "Unknown error occurred";
        accumulatedAiText = `Error: ${errorText}`;
        finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
          msg.id === aiMessageId
            ? {
                ...msg,
                text: accumulatedAiText,
                isLoading: false,
                isError: true,
              }
            : msg,
        );

        // Save error state to database
        onUpdateChat(currentChatIdToUse, finalMessagesForApp, {
          skipSave: false,
        });
      }

      setIsLoading(false);
    },
    [
      activeChatId,
      setActiveChatId,
      currentUser,
      messages,
      onUpdateChat,
      selectedModel,
      selectedAssistantId,
    ],
  );

  return (
    <div
      className="flex-1 flex flex-col bg-slate-50 dark:bg-slate-900 min-h-0"
      data-oid="azwds6j"
    >
      <MainHeader
        selectedModel={selectedModel}
        onModelChange={handleModelChange}
        isSidebarOpen={isSidebarOpen}
        onToggleSidebar={onToggleSidebar}
        onNewChat={onNewChat}
        navigateTo={navigateTo}
        isAuthenticated={isAuthenticated}
        currentUser={currentUser}
        handleLogout={handleLogout}
        openSignInModal={openSignInModal}
        data-oid="xijlvpe"
      />

      {messages.length === 0 &&
      !activeChatId &&
      !assessmentPreview?.isActive ? (
        <div className="flex-1 flex flex-col min-h-0" data-oid="0zbvza6">
          <WelcomeScreen
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            isAuthenticated={isAuthenticated}
            currentUser={currentUser}
            selectedAssistant={selectedAssistant}
            onSelectAssessment={handleSelectAssessment}
            data-oid="4a1bcl6"
          />

          <Footer isAuthenticated={isAuthenticated} data-oid="du_midw" />
        </div>
      ) : (
        <div
          className="flex-1 flex flex-col min-h-0 overflow-hidden"
          data-oid="t4-cg0z"
        >
          {/* Assistant Info Bar */}

          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto message-scrollbar"
            data-oid="v.22p:6"
          >
            <MessageDisplay
              messages={messages}
              onAssessmentAnswer={handleAssessmentAnswer}
              activeChatId={activeChatId || undefined}
              data-oid="abzsmvn"
            />
          </div>
          <div
            className="p-3 bg-slate-50 dark:bg-slate-900 border-t border-slate-200 dark:border-slate-700/50 flex-shrink-0"
            data-oid="4u91eky"
          >
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              data-oid="nzrufj9"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default MainArea;
