import { supabase } from '../lib/supabase';
import {
  Assessment,
  AssessmentQuestion,
  QuickAnswer,
  AssessmentResponse,
  AssessmentSession,
  AssessmentResult,
  AssessmentScores,
  SixHumanNeedsScores,
  AssessmentInsight,
  AssessmentListResponse,
  AssessmentDetailResponse,
  AssessmentResultResponse,
  CreateAssessmentRequest,
  AssessmentError,
  ASSESSMENT_TYPES,
  SIX_HUMAN_NEEDS_CATEGORIES
} from '../types/assessment';

class AssessmentService {
  private static instance: AssessmentService;

  static getInstance(): AssessmentService {
    if (!AssessmentService.instance) {
      AssessmentService.instance = new AssessmentService();
    }
    return AssessmentService.instance;
  }

  /**
   * Get all active assessments
   */
  async getAssessments(): Promise<AssessmentListResponse> {
    try {
      console.log('🔍 [AssessmentService] Fetching assessments from Supabase...');

      const { data, error } = await supabase
        .from('assessments')
        .select('*')
        .eq('is_active', true)
        .order('title');

      console.log('📊 [AssessmentService] Supabase response:', { data, error });

      if (error) {
        console.error('❌ [AssessmentService] Supabase error:', error);
        throw error;
      }

      console.log('✅ [AssessmentService] Successfully fetched assessments:', data?.length || 0);

      return {
        assessments: data || [],
        total: data?.length || 0
      };
    } catch (error) {
      console.error('❌ [AssessmentService] Error fetching assessments:', error);
      throw this.createError('FETCH_ASSESSMENTS_ERROR', 'Failed to fetch assessments', error);
    }
  }

  /**
   * Get assessment details with questions and answers
   */
  async getAssessmentDetail(assessmentId: string): Promise<AssessmentDetailResponse> {
    try {
      console.log('🔍 [AssessmentService] Fetching assessment detail for ID:', assessmentId);

      // Fetch assessment
      const { data: assessment, error: assessmentError } = await supabase
        .from('assessments')
        .select('*')
        .eq('id', assessmentId)
        .single();

      console.log('📊 [AssessmentService] Assessment fetch result:', { assessment, assessmentError });

      if (assessmentError) {
        console.error('❌ [AssessmentService] Assessment fetch error:', assessmentError);
        throw assessmentError;
      }

      // Fetch questions with quick answers
      const { data: questions, error: questionsError } = await supabase
        .from('assessment_questions')
        .select(`
          *,
          assessment_quick_answers (*)
        `)
        .eq('assessment_id', assessmentId)
        .order('order');

      if (questionsError) throw questionsError;

      // Transform the data to match our types
      const transformedQuestions: AssessmentQuestion[] = questions?.map(q => ({
        id: q.id.toString(),
        assessment_id: q.assessment_id.toString(),
        category: q.category,
        question_text: q.question_text,
        follow_up_text: q.follow_up_text,
        order: q.order,
        quick_answers: q.assessment_quick_answers?.map((qa: any) => ({
          id: qa.id.toString(),
          question_id: qa.question_id.toString(),
          answer_text: qa.answer_text,
          score_value: qa.score_value,
          order: qa.order
        })).sort((a: QuickAnswer, b: QuickAnswer) => a.order - b.order) || []
      })) || [];

      return {
        assessment: {
          id: assessment.id.toString(),
          title: assessment.title,
          description: assessment.description,
          icon: assessment.icon,
          type: assessment.type,
          is_active: assessment.is_active,
          questions: transformedQuestions
        },
        questions: transformedQuestions
      };
    } catch (error) {
      console.error('Error fetching assessment detail:', error);
      throw this.createError('FETCH_ASSESSMENT_DETAIL_ERROR', 'Failed to fetch assessment details', error);
    }
  }

  /**
   * Save assessment response
   */
  async saveAssessmentResponse(
    sessionId: string,
    questionId: string,
    quickAnswerId?: string,
    freeTextResponse?: string,
    score?: number
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          chat_session_id: sessionId,
          content: freeTextResponse || '',
          sender_type: 'user',
          question_id: parseInt(questionId),
          quick_answer_id: quickAnswerId ? parseInt(quickAnswerId) : null,
          score: score || 0,
          is_assessment: true,
          is_free_text: !!freeTextResponse,
          timestamp: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error saving assessment response:', error);
      throw this.createError('SAVE_RESPONSE_ERROR', 'Failed to save assessment response', error);
    }
  }

  /**
   * Calculate 6HN scores from responses
   */
  calculate6HNScores(responses: AssessmentResponse[]): SixHumanNeedsScores {
    const categoryScores: Record<string, number[]> = {
      'Certainty': [],
      'Uncertainty/Variety': [],
      'Significance': [],
      'Connection/Love': [],
      'Growth': [],
      'Contribution': []
    };

    // Group responses by category and collect scores
    responses.forEach(response => {
      const category = this.getQuestionCategory(response.question_id);
      if (categoryScores[category]) {
        // Normalize score to 1-10 scale if needed
        const normalizedScore = this.normalizeScore(response.score);
        categoryScores[category].push(normalizedScore);
      }
    });

    // Calculate weighted average scores for each category
    const calculateWeightedAverage = (scores: number[]) => {
      if (scores.length === 0) return 0;

      // Apply weighting based on response consistency and confidence
      const sum = scores.reduce((total, score) => total + score, 0);
      const average = sum / scores.length;

      // Apply confidence factor based on number of responses
      const confidenceFactor = Math.min(scores.length / 4, 1); // Assume 4 questions per category

      return Math.round((average * confidenceFactor) * 10) / 10; // Round to 1 decimal
    };

    return {
      certainty: calculateWeightedAverage(categoryScores['Certainty']),
      uncertainty_variety: calculateWeightedAverage(categoryScores['Uncertainty/Variety']),
      significance: calculateWeightedAverage(categoryScores['Significance']),
      connection_love: calculateWeightedAverage(categoryScores['Connection/Love']),
      growth: calculateWeightedAverage(categoryScores['Growth']),
      contribution: calculateWeightedAverage(categoryScores['Contribution'])
    };
  }

  /**
   * Normalize score to 1-10 scale
   */
  private normalizeScore(score: number): number {
    // Assuming scores come in as 1-3 scale from quick answers
    // Map to 1-10 scale: 1->2-4, 2->5-7, 3->8-10
    if (score <= 1) return 3;
    if (score <= 2) return 6;
    if (score <= 3) return 9;
    return Math.max(1, Math.min(10, score)); // Clamp to 1-10 range
  }

  /**
   * Generate assessment insights
   */
  generateAssessmentInsights(scores: SixHumanNeedsScores): AssessmentInsight[] {
    const insights: AssessmentInsight[] = [];
    
    const traits = [
      { key: 'certainty', name: 'Certainty', score: scores.certainty },
      { key: 'uncertainty_variety', name: 'Uncertainty/Variety', score: scores.uncertainty_variety },
      { key: 'significance', name: 'Significance', score: scores.significance },
      { key: 'connection_love', name: 'Connection/Love', score: scores.connection_love },
      { key: 'growth', name: 'Growth', score: scores.growth },
      { key: 'contribution', name: 'Contribution', score: scores.contribution }
    ];

    traits.forEach(trait => {
      insights.push({
        trait: trait.name,
        score: trait.score,
        description: this.getTraitDescription(trait.key, trait.score),
        recommendations: this.getTraitRecommendations(trait.key, trait.score)
      });
    });

    return insights.sort((a, b) => b.score - a.score);
  }

  /**
   * Save assessment result
   */
  async saveAssessmentResult(
    sessionId: string,
    assessmentId: string,
    scores: AssessmentScores
  ): Promise<AssessmentResult> {
    try {
      const { data, error } = await supabase
        .from('assessment_results')
        .insert({
          session_id: sessionId,
          assessment_id: parseInt(assessmentId),
          result_json: scores,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id.toString(),
        session_id: data.session_id,
        assessment_id: data.assessment_id.toString(),
        result_json: data.result_json,
        created_at: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Error saving assessment result:', error);
      throw this.createError('SAVE_RESULT_ERROR', 'Failed to save assessment result', error);
    }
  }

  /**
   * Get assessment results for a session
   */
  async getAssessmentResults(sessionId: string): Promise<AssessmentResultResponse[]> {
    try {
      const { data, error } = await supabase
        .from('assessment_results')
        .select(`
          *,
          assessments (title, type, icon)
        `)
        .eq('session_id', sessionId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data?.map(result => ({
        result: {
          id: result.id.toString(),
          session_id: result.session_id,
          assessment_id: result.assessment_id.toString(),
          result_json: result.result_json,
          created_at: new Date(result.created_at)
        },
        insights: result.result_json.insights || []
      })) || [];
    } catch (error) {
      console.error('Error fetching assessment results:', error);
      throw this.createError('FETCH_RESULTS_ERROR', 'Failed to fetch assessment results', error);
    }
  }

  // Helper methods
  private getQuestionCategory(questionId: string): string {
    // Map question IDs to categories based on the 6HN questionnaire structure
    // This is a simplified mapping - in production, this would come from the database
    const questionCategoryMap: Record<string, string> = {
      // Certainty questions (questions about stability, control, security)
      '1': 'Certainty', '7': 'Certainty', '13': 'Certainty', '19': 'Certainty',
      // Uncertainty/Variety questions (questions about excitement, adventure, change)
      '2': 'Uncertainty/Variety', '8': 'Uncertainty/Variety', '14': 'Uncertainty/Variety', '20': 'Uncertainty/Variety',
      // Significance questions (questions about recognition, uniqueness, importance)
      '3': 'Significance', '9': 'Significance', '15': 'Significance', '21': 'Significance',
      // Connection/Love questions (questions about relationships, belonging, intimacy)
      '4': 'Connection/Love', '10': 'Connection/Love', '16': 'Connection/Love', '22': 'Connection/Love',
      // Growth questions (questions about learning, improvement, development)
      '5': 'Growth', '11': 'Growth', '17': 'Growth', '23': 'Growth',
      // Contribution questions (questions about giving, impact, legacy)
      '6': 'Contribution', '12': 'Contribution', '18': 'Contribution', '24': 'Contribution'
    };

    return questionCategoryMap[questionId] || 'Certainty';
  }

  private getTraitDescription(trait: string, score: number): string {
    const descriptions: Record<string, Record<string, string>> = {
      certainty: {
        high: "You have a strong need for stability, security, and predictability in your life. You value routines and feel most comfortable when you know what to expect.",
        medium: "You appreciate some structure while remaining open to change. You balance the need for security with flexibility.",
        low: "You're comfortable with uncertainty and don't require much structure. You adapt easily to changing circumstances."
      },
      uncertainty_variety: {
        high: "You thrive on change, adventure, and new experiences. You actively seek variety and can become restless with too much routine.",
        medium: "You enjoy variety while maintaining some routine. You appreciate new experiences but also value some predictability.",
        low: "You prefer consistency and familiar patterns. Too much change or unpredictability can feel overwhelming."
      },
      significance: {
        high: "You have a strong need to feel important, unique, and valued. Recognition and acknowledgment are very important to you.",
        medium: "You appreciate recognition but don't constantly seek it. You have a healthy sense of your own worth.",
        low: "You don't require much external validation. You're comfortable being in the background and don't need to stand out."
      },
      connection_love: {
        high: "Deep relationships and emotional connections are central to your well-being. You prioritize love and belonging above most other needs.",
        medium: "You value relationships while maintaining some independence. You enjoy close connections but also need personal space.",
        low: "You're comfortable with solitude and don't require intense emotional connections. You value independence over intimacy."
      },
      growth: {
        high: "Personal development and continuous learning are essential to you. You're always seeking to improve and expand your capabilities.",
        medium: "You enjoy learning and growing but don't feel compelled to constantly challenge yourself. You balance growth with contentment.",
        low: "You're satisfied with your current level of knowledge and skills. You don't feel a strong drive for constant self-improvement."
      },
      contribution: {
        high: "Making a positive impact on others and the world is a core driver for you. You find deep fulfillment in giving and serving.",
        medium: "You enjoy helping others when you can but also focus on your own needs. You balance giving with self-care.",
        low: "You focus primarily on your own needs and goals. Contributing to others is not a major motivator for you."
      }
    };

    const level = score >= 7 ? 'high' : score >= 4 ? 'medium' : 'low';
    return descriptions[trait]?.[level] || "No description available for this trait.";
  }

  private getTraitRecommendations(trait: string, score: number): string[] {
    const recommendations: Record<string, Record<string, string[]>> = {
      certainty: {
        high: [
          "Create structured daily routines that provide stability",
          "Build emergency funds and backup plans for security",
          "Practice mindfulness to stay grounded in the present"
        ],
        medium: [
          "Balance structure with flexibility in your schedule",
          "Develop contingency plans while staying open to change",
          "Practice gradual exposure to new experiences"
        ],
        low: [
          "Consider adding some routine to provide stability",
          "Build basic security measures for peace of mind",
          "Practice grounding techniques during stressful times"
        ]
      },
      uncertainty_variety: {
        high: [
          "Plan regular adventures and new experiences",
          "Change your environment or routine periodically",
          "Take on challenging projects that push your boundaries"
        ],
        medium: [
          "Introduce small changes to break up routine",
          "Try new hobbies or activities monthly",
          "Travel to new places when possible"
        ],
        low: [
          "Start with small, manageable changes",
          "Gradually introduce variety into your routine",
          "Find excitement in familiar activities"
        ]
      },
      significance: {
        high: [
          "Seek leadership roles and opportunities to stand out",
          "Develop unique skills that set you apart",
          "Share your achievements and celebrate successes"
        ],
        medium: [
          "Find healthy ways to gain recognition for your work",
          "Develop your unique talents and strengths",
          "Contribute to projects where you can make a visible impact"
        ],
        low: [
          "Focus on internal validation and self-worth",
          "Find fulfillment in behind-the-scenes contributions",
          "Appreciate your unique qualities without needing external recognition"
        ]
      },
      connection_love: {
        high: [
          "Prioritize quality time with loved ones",
          "Develop deep, meaningful relationships",
          "Practice vulnerability and emotional intimacy"
        ],
        medium: [
          "Balance social time with personal space",
          "Nurture existing relationships while making new connections",
          "Practice both giving and receiving support"
        ],
        low: [
          "Gradually open up to others at your own pace",
          "Find ways to connect that feel comfortable to you",
          "Appreciate the relationships you do have"
        ]
      },
      growth: {
        high: [
          "Set challenging learning goals and pursue them actively",
          "Seek feedback and use it for continuous improvement",
          "Take on stretch assignments that develop new skills"
        ],
        medium: [
          "Balance learning with applying what you already know",
          "Set realistic development goals",
          "Find mentors or coaches to guide your growth"
        ],
        low: [
          "Appreciate your current skills and knowledge",
          "Focus on mastering what you already do well",
          "Consider small, incremental improvements"
        ]
      },
      contribution: {
        high: [
          "Volunteer for causes you care about",
          "Mentor others and share your knowledge",
          "Look for ways to make a positive impact in your community"
        ],
        medium: [
          "Find opportunities to help others when you can",
          "Balance giving with taking care of your own needs",
          "Contribute your unique skills to meaningful projects"
        ],
        low: [
          "Start with small acts of kindness",
          "Focus on contributing to your immediate circle",
          "Remember that taking care of yourself enables you to help others"
        ]
      }
    };

    const level = score >= 7 ? 'high' : score >= 4 ? 'medium' : 'low';
    return recommendations[trait]?.[level] || [
      "Focus on understanding this trait better",
      "Consider how this trait shows up in your daily life",
      "Explore ways to develop or balance this aspect of yourself"
    ];
  }

  private createError(code: string, message: string, details?: any): AssessmentError {
    return {
      code,
      message,
      details
    };
  }
}

export const assessmentService = AssessmentService.getInstance();
