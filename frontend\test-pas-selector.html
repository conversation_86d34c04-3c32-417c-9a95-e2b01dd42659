<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PAS Selector Test - Desktop 3-Card Layout</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for demonstration */
        .card-container {
            perspective: 1000px;
        }
        
        .assessment-card {
            transition: transform 0.3s ease-out, opacity 0.3s ease-out;
            touch-action: pan-y;
        }
        
        .assessment-card:hover {
            transform: scale(1.02);
        }
        
        /* Mobile-first responsive design */
        @media (max-width: 767px) {
            .mobile-single-card {
                width: 100%;
            }
        }
        
        @media (min-width: 768px) {
            .desktop-three-cards {
                width: 33.333%;
            }
        }
    </style>
</head>
<body class="bg-slate-100 dark:bg-slate-900 min-h-screen">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold text-center mb-8 text-slate-800 dark:text-slate-100">
            PAS Selector - Responsive Layout Test
        </h1>
        
        <!-- Instructions -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-6 mb-8 shadow-lg">
            <h2 class="text-xl font-semibold mb-4 text-slate-800 dark:text-slate-100">Test Instructions</h2>
            <div class="space-y-2 text-slate-600 dark:text-slate-300">
                <p><strong>Desktop (≥768px):</strong> Should display 3 cards side by side</p>
                <p><strong>Mobile (<768px):</strong> Should display 1 card at a time</p>
                <p><strong>Swipe:</strong> Works on both desktop and mobile</p>
                <p><strong>Navigation:</strong> Dots and arrows adapt to the layout</p>
            </div>
        </div>
        
        <!-- Current viewport info -->
        <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-4 mb-8">
            <p class="text-blue-800 dark:text-blue-200">
                <strong>Current viewport:</strong> 
                <span id="viewport-info">Loading...</span>
            </p>
            <p class="text-blue-800 dark:text-blue-200">
                <strong>Layout mode:</strong> 
                <span id="layout-mode">Loading...</span>
            </p>
        </div>
        
        <!-- PAS Selector Demo -->
        <div class="flex flex-col items-center w-full mx-auto" id="pas-container">
            <h2 class="text-xl font-bold text-blue-700 dark:text-blue-200 lg:mb-4 sm:mb-1 mb-4">
                Personality Assessments
            </h2>
            
            <!-- Swipe hint for mobile users -->
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-2 sm:hidden">
                Swipe left or right to explore
            </p>
            
            <!-- Card container -->
            <div class="w-full rounded-lg card-container" id="card-container">
                <!-- Cards will be inserted here by JavaScript -->
            </div>
            
            <!-- Navigation indicators -->
            <div class="flex space-x-2 mt-2" id="indicators">
                <!-- Indicators will be inserted here by JavaScript -->
            </div>
            
            <!-- Navigation arrows for desktop -->
            <div class="hidden sm:flex justify-between mt-2 w-full" id="nav-arrows">
                <button id="prev-btn" class="p-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <button id="next-btn" class="p-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Feature checklist -->
        <div class="mt-12 bg-white dark:bg-slate-800 rounded-lg p-6 shadow-lg">
            <h3 class="text-lg font-semibold mb-4 text-slate-800 dark:text-slate-100">Implementation Checklist</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">✓</span>
                        <span class="text-slate-600 dark:text-slate-300">Desktop: 3 cards side by side</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">✓</span>
                        <span class="text-slate-600 dark:text-slate-300">Mobile: 1 card at a time</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">✓</span>
                        <span class="text-slate-600 dark:text-slate-300">Responsive breakpoints (768px)</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">✓</span>
                        <span class="text-slate-600 dark:text-slate-300">Swipe gestures preserved</span>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">✓</span>
                        <span class="text-slate-600 dark:text-slate-300">Navigation controls adapt</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">✓</span>
                        <span class="text-slate-600 dark:text-slate-300">State management maintained</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">✓</span>
                        <span class="text-slate-600 dark:text-slate-300">Smooth transitions</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">✓</span>
                        <span class="text-slate-600 dark:text-slate-300">Mobile-first design</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Assessment data
        const assessments = [
            {
                id: '6hn',
                title: '6 Human Needs',
                description: 'Discover your core human needs and how they shape your life.',
                color: 'from-blue-500 to-purple-600'
            },
            {
                id: 'mbti',
                title: 'MBTI',
                description: 'Myers-Briggs Type Indicator personality assessment.',
                color: 'from-green-500 to-teal-600'
            },
            {
                id: 'disc',
                title: 'DiSC',
                description: 'DiSC behavioral assessment for teams and individuals.',
                color: 'from-orange-500 to-red-600'
            }
        ];
        
        // State
        let currentIndex = 0;
        let isDesktop = window.innerWidth >= 768;
        let isTransitioning = false;
        
        // DOM elements
        const container = document.getElementById('pas-container');
        const cardContainer = document.getElementById('card-container');
        const indicators = document.getElementById('indicators');
        const navArrows = document.getElementById('nav-arrows');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const viewportInfo = document.getElementById('viewport-info');
        const layoutMode = document.getElementById('layout-mode');
        
        // Update viewport info
        function updateViewportInfo() {
            const width = window.innerWidth;
            viewportInfo.textContent = `${width}px`;
            layoutMode.textContent = width >= 768 ? 'Desktop (3 cards)' : 'Mobile (1 card)';
            isDesktop = width >= 768;
            
            // Update container max-width
            container.className = `flex flex-col items-center w-full mx-auto ${isDesktop ? 'max-w-4xl' : 'max-w-md'}`;
            
            // Update card container height
            cardContainer.className = `relative w-full overflow-hidden rounded-lg card-container ${isDesktop ? 'h-48' : 'h-40'}`;
            
            // Update nav arrows width
            navArrows.className = `hidden sm:flex justify-between mt-2 ${isDesktop ? 'w-full max-w-4xl' : 'w-full max-w-xs'}`;
            
            renderCards();
            renderIndicators();
            updateNavigation();
        }
        
        // Render cards
        function renderCards() {
            cardContainer.innerHTML = '';

            // Update container classes based on layout
            if (isDesktop) {
                cardContainer.className = 'w-full rounded-lg card-container flex justify-center items-center gap-4 min-h-48';

                // Desktop: Show 3 cards side by side
                const visibleAssessments = assessments.slice(currentIndex, currentIndex + 3);
                visibleAssessments.forEach((assessment, relativeIndex) => {
                    const actualIndex = currentIndex + relativeIndex;
                    const style = getCardStyle(actualIndex);

                    const cardDiv = document.createElement('div');
                    cardDiv.className = `flex-1 max-w-sm transition-opacity duration-300 ${style.className}`;
                    cardDiv.style.transition = style.transition;
                    cardDiv.style.zIndex = style.zIndex;

                    const card = document.createElement('button');
                    card.className = `assessment-card w-full flex flex-col items-center justify-center bg-gradient-to-br ${assessment.color} rounded-lg shadow-md p-4 text-white hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white transition-shadow select-none`;

                    card.innerHTML = `
                        <span class="font-semibold text-lg mb-1">${assessment.title}</span>
                        <span class="text-sm text-center opacity-90">${assessment.description}</span>
                    `;

                    card.addEventListener('click', () => {
                        alert(`Selected: ${assessment.title}`);
                    });

                    cardDiv.appendChild(card);
                    cardContainer.appendChild(cardDiv);
                });
            } else {
                cardContainer.className = 'w-full rounded-lg card-container relative h-40 overflow-hidden';
                cardContainer.style.perspective = '1000px';

                // Mobile: Show all cards with absolute positioning
                assessments.forEach((assessment, index) => {
                    const style = getCardStyle(index);

                    const cardDiv = document.createElement('div');
                    cardDiv.className = `absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${style.className}`;
                    cardDiv.style.transform = style.transform;
                    cardDiv.style.transition = style.transition;
                    cardDiv.style.zIndex = style.zIndex;

                    const card = document.createElement('button');
                    card.className = `assessment-card w-full max-w-xs mx-4 flex flex-col items-center justify-center bg-gradient-to-br ${assessment.color} rounded-lg shadow-md p-4 text-white hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white transition-shadow select-none`;

                    card.innerHTML = `
                        <span class="font-semibold text-lg mb-1">${assessment.title}</span>
                        <span class="text-sm text-center opacity-90">${assessment.description}</span>
                    `;

                    card.addEventListener('click', () => {
                        alert(`Selected: ${assessment.title}`);
                    });

                    cardDiv.appendChild(card);
                    cardContainer.appendChild(cardDiv);
                });
            }
        }
        
        // Get card style based on current layout
        function getCardStyle(index) {
            if (isDesktop) {
                // Desktop: Show 3 cards side by side as separate tiles
                const offset = index - currentIndex;
                const isVisible = offset >= 0 && offset <= 2;

                return {
                    transform: '',
                    transition: isTransitioning ? 'opacity 0.3s ease-out' : '',
                    className: isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none',
                    zIndex: isVisible ? 10 : 1
                };
            } else {
                // Mobile: Show 1 card at a time
                const offset = index - currentIndex;
                const translateX = offset * 100;
                const isVisible = Math.abs(offset) <= 1;
                const isCurrent = offset === 0;
                const scale = isCurrent ? 1 : 0.9;

                return {
                    transform: `translateX(${translateX}%) scale(${scale})`,
                    transition: isTransitioning ? 'transform 0.3s ease-out, opacity 0.3s ease-out' : '',
                    className: isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none',
                    zIndex: isCurrent ? 10 : Math.abs(offset) === 1 ? 5 : 1
                };
            }
        }
        
        // Render indicators
        function renderIndicators() {
            indicators.innerHTML = '';
            
            if (isDesktop) {
                // Desktop: Show indicators for sets of 3 cards
                const numSets = Math.ceil(assessments.length / 3);
                for (let setIndex = 0; setIndex < numSets; setIndex++) {
                    const indicator = document.createElement('button');
                    indicator.className = `w-2 h-2 rounded-full transition-colors duration-200 ${
                        Math.floor(currentIndex / 3) === setIndex
                            ? 'bg-blue-600 dark:bg-blue-400'
                            : 'bg-gray-300 dark:bg-gray-600'
                    }`;
                    indicator.addEventListener('click', () => goToIndex(setIndex * 3));
                    indicators.appendChild(indicator);
                }
            } else {
                // Mobile: Show indicators for individual cards
                assessments.forEach((_, index) => {
                    const indicator = document.createElement('button');
                    indicator.className = `w-2 h-2 rounded-full transition-colors duration-200 ${
                        index === currentIndex
                            ? 'bg-blue-600 dark:bg-blue-400'
                            : 'bg-gray-300 dark:bg-gray-600'
                    }`;
                    indicator.addEventListener('click', () => goToIndex(index));
                    indicators.appendChild(indicator);
                });
            }
        }
        
        // Navigation functions
        function goToNext() {
            const step = isDesktop ? 3 : 1;
            const maxIndex = assessments.length - 1;
            
            if (currentIndex < maxIndex && !isTransitioning) {
                isTransitioning = true;
                currentIndex = Math.min(currentIndex + step, maxIndex);
                renderCards();
                renderIndicators();
                updateNavigation();
                setTimeout(() => { isTransitioning = false; }, 300);
            }
        }
        
        function goToPrevious() {
            const step = isDesktop ? 3 : 1;
            
            if (currentIndex > 0 && !isTransitioning) {
                isTransitioning = true;
                currentIndex = Math.max(currentIndex - step, 0);
                renderCards();
                renderIndicators();
                updateNavigation();
                setTimeout(() => { isTransitioning = false; }, 300);
            }
        }
        
        function goToIndex(index) {
            const clampedIndex = Math.max(0, Math.min(index, assessments.length - 1));
            if (clampedIndex !== currentIndex && !isTransitioning) {
                isTransitioning = true;
                currentIndex = clampedIndex;
                renderCards();
                renderIndicators();
                updateNavigation();
                setTimeout(() => { isTransitioning = false; }, 300);
            }
        }
        
        // Update navigation button states
        function updateNavigation() {
            prevBtn.disabled = currentIndex === 0 || isTransitioning;
            nextBtn.disabled = (isDesktop ? currentIndex + 3 >= assessments.length : currentIndex === assessments.length - 1) || isTransitioning;
        }
        
        // Event listeners
        prevBtn.addEventListener('click', goToPrevious);
        nextBtn.addEventListener('click', goToNext);
        
        window.addEventListener('resize', updateViewportInfo);
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                goToPrevious();
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                goToNext();
            }
        });
        
        // Initialize
        updateViewportInfo();
    </script>
</body>
</html>
