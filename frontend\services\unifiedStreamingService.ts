// services/unifiedStreamingService.ts
import { AIStreamChunk, AIMessageRequest } from './aiStreamCore';

export interface UnifiedAIResponse {
  text: string;
  metadata?: {
    groundingChunks?: any[];
    usage?: {
      promptTokens?: number;
      completionTokens?: number;
    };
  };
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}

export interface AIProvider {
  sendMessage(request: AIMessageRequest): Promise<UnifiedAIResponse>;
  supportsFeature(feature: string): boolean;
}

/**
 * Unified Streaming Service
 * Handles streaming by breaking down complete responses into chunks
 */
export class UnifiedStreamingService {
  private providers = new Map<string, AIProvider>();
  private defaultProvider: string = 'gemini';

  registerProvider(name: string, provider: AIProvider) {
    this.providers.set(name, provider);
  }

  setDefaultProvider(name: string) {
    if (!this.providers.has(name)) {
      throw new Error(`Provider ${name} not registered`);
    }
    this.defaultProvider = name;
  }

  getProvider(name?: string): AIProvider {
    const providerName = name || this.defaultProvider;
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not available`);
    }
    return provider;
  }

  /**
   * Stream a complete response by breaking it into chunks
   */
  async *sendStream(
    request: AIMessageRequest,
    providerName?: string,
    chunkDelay: number = 50 // Delay between chunks in milliseconds
  ): AsyncIterable<AIStreamChunk> {
    console.log(`🔄 [UnifiedStreamingService] Starting stream with provider: ${providerName || 'default'}`);
    const provider = this.getProvider(providerName);
    console.log(`✅ [UnifiedStreamingService] Provider found:`, provider.constructor.name);
    
    try {
      // Get complete response from provider
      console.log(`📤 [UnifiedStreamingService] Sending request to provider:`, request);
      const response = await provider.sendMessage(request);
      console.log(`📥 [UnifiedStreamingService] Received response:`, response);
      
      if (response.error) {
        yield {
          text: '',
          isComplete: true,
          error: response.error
        };
        return;
      }

      // Break response into chunks and stream them
      const text = response.text;
      const chunkSize = 10; // Characters per chunk
      
      for (let i = 0; i < text.length; i += chunkSize) {
        const chunk = text.slice(i, i + chunkSize);
        
        yield {
          text: chunk,
          isComplete: false,
          metadata: response.metadata
        };

        // Add delay between chunks for smooth streaming effect
        if (i + chunkSize < text.length) {
          await new Promise(resolve => setTimeout(resolve, chunkDelay));
        }
      }

      // Final completion marker
      yield { 
        text: '', 
        isComplete: true,
        metadata: response.metadata
      };

    } catch (error) {
      yield {
        text: '',
        isComplete: true,
        error: {
          code: 'unified_error',
          message: error instanceof Error ? error.message : 'Unknown error',
          retryable: true
        }
      };
    }
  }
}

// Singleton instance
export const unifiedStreamingService = new UnifiedStreamingService();

// Chat session management
let currentChatInstance: any = null;

export const resetChatSession = () => {
  currentChatInstance = null;
};

export const updateAssistantContext = (systemInstruction: string, parameters?: Record<string, any>) => {
  // This function can be used to update context for providers that support it
  // For now, we'll reset the chat session to force recreation with new context
  resetChatSession();
}; 