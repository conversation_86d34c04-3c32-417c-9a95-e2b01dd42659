// Assessment System Type Definitions

export interface Assessment {
  id: string;
  title: string;
  description: string;
  icon?: string;
  type: string; // e.g., '6HN', 'MBTI', 'DiSC'
  is_active: boolean;
  questions?: AssessmentQuestion[];
}

export interface AssessmentQuestion {
  id: string;
  assessment_id: string;
  category: string;
  question_text: string;
  follow_up_text?: string;
  order: number;
  quick_answers?: QuickAnswer[];
}

export interface QuickAnswer {
  id: string;
  answer_text: string;
  score_value: number;
  order: number;
}

export interface AssessmentResponse {
  question_id: string;
  quick_answer_id?: string;
  free_text_response?: string;
  score: number;
  timestamp: Date;
}

export interface AssessmentSession {
  id: string;
  assessment_id: string;
  session_id: string; // Chat session ID
  responses: AssessmentResponse[];
  current_question_index: number;
  is_completed: boolean;
  started_at: Date;
  completed_at?: Date;
}

export interface AssessmentResult {
  id: string;
  session_id: string;
  assessment_id: string;
  result_json: AssessmentScores;
  created_at: Date;
}

// 6HN Specific Types
export interface SixHumanNeedsScores {
  certainty: number;
  uncertainty_variety: number;
  significance: number;
  connection_love: number;
  growth: number;
  contribution: number;
}

export interface AssessmentScores {
  type: '6HN' | 'MBTI' | 'DiSC';
  scores: SixHumanNeedsScores | Record<string, number>;
  insights: AssessmentInsight[];
  dominant_traits: string[];
  completion_percentage: number;
}

export interface AssessmentInsight {
  trait: string;
  score: number;
  description: string;
  recommendations: string[];
}

// UI State Types
export interface AssessmentFlowState {
  currentAssessment: Assessment | null;
  currentQuestion: AssessmentQuestion | null;
  currentQuestionIndex: number;
  totalQuestions: number;
  responses: AssessmentResponse[];
  isLoading: boolean;
  error: string | null;
  isCompleted: boolean;
}

// Assessment Message Types (for chat integration)
export interface AssessmentMessage {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  question_id?: string;
  quick_answer_id?: string;
  score?: number;
  is_assessment: boolean;
  is_free_text: boolean;
  chatId: string;
}

// API Response Types
export interface AssessmentListResponse {
  assessments: Assessment[];
  total: number;
}

export interface AssessmentDetailResponse {
  assessment: Assessment;
  questions: AssessmentQuestion[];
}

export interface AssessmentResultResponse {
  result: AssessmentResult;
  insights: AssessmentInsight[];
}

// Assessment Configuration
export interface AssessmentConfig {
  allowSkipQuestions: boolean;
  requireAllQuestions: boolean;
  showProgressBar: boolean;
  enableFollowUpQuestions: boolean;
  maxResponseLength: number;
  timeoutMinutes?: number;
}

// Team Assessment Types (for future use)
export interface Team {
  id: string;
  name: string;
  created_at: Date;
}

export interface TeamMember {
  id: string;
  team_id: string;
  user_id: string;
  joined_at: Date;
}

export interface TeamAssessmentResult {
  team_id: string;
  assessment_id: string;
  individual_results: AssessmentResult[];
  team_summary: AssessmentScores;
  created_at: Date;
}

// Utility Types
export type AssessmentStatus = 'not_started' | 'in_progress' | 'completed' | 'abandoned';
export type QuestionType = 'multiple_choice' | 'text' | 'scale' | 'boolean';
export type AssessmentCategory = 'personality' | 'skills' | 'values' | 'behavior';

// Form Types for Assessment Creation/Editing
export interface CreateAssessmentRequest {
  title: string;
  description: string;
  type: string;
  icon?: string;
  questions: CreateQuestionRequest[];
}

export interface CreateQuestionRequest {
  category: string;
  question_text: string;
  follow_up_text?: string;
  order: number;
  quick_answers: CreateQuickAnswerRequest[];
}

export interface CreateQuickAnswerRequest {
  answer_text: string;
  score_value: number;
  order: number;
}

// Error Types
export interface AssessmentError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Constants
export const ASSESSMENT_TYPES = {
  SIX_HUMAN_NEEDS: '6HN',
  MBTI: 'MBTI',
  DISC: 'DiSC'
} as const;

export const SIX_HUMAN_NEEDS_CATEGORIES = {
  CERTAINTY: 'Certainty',
  UNCERTAINTY_VARIETY: 'Uncertainty/Variety',
  SIGNIFICANCE: 'Significance',
  CONNECTION_LOVE: 'Connection/Love',
  GROWTH: 'Growth',
  CONTRIBUTION: 'Contribution'
} as const;

export type AssessmentType = typeof ASSESSMENT_TYPES[keyof typeof ASSESSMENT_TYPES];
export type SixHumanNeedsCategory = typeof SIX_HUMAN_NEEDS_CATEGORIES[keyof typeof SIX_HUMAN_NEEDS_CATEGORIES];
