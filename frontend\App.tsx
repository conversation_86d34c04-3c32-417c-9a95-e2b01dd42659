import { useState, useCallback, useEffect } from "react";
import Sidebar from "./components/Sidebar";
import MainArea from "./components/MainArea";
import SettingsPage from "./components/SettingsPage";
import SignInModal from "./components/SignInModal";
import SignInEmailModal from "./components/SignInEmailModal";
import SignUpEmailModal from "./components/SignUpEmailModal";
import EmailVerificationModal from "./components/EmailVerificationModal";
import ResetPasswordModal from "./components/ResetPasswordModal";
import ChatHistoryAccessModal from "./components/ChatHistoryAccessModal";
import AccountModal from "./components/AccountModal";
import { supabase } from "./lib/supabase";
import { resetChatSession } from "./services/unifiedStreamingService";
import { deduplicateMessages } from "./utils/deduplicateMessages";
import { dataService } from "./services/dataService";
import { connectionService } from "./services/connectionService";
import { userService } from "./services/userService";
import { initializeProviders } from "./services/providers";

import { AuthView, ChatSession, User, Message, UserProfiles } from "./types";
import { getCurrentUser } from "./constants";
import { useTranslation } from "react-i18next";
import { Routes, Route } from "react-router-dom";
import ResetPasswordPage from "./components/ResetPasswordPage";
import ResetPasswordSuccessPage from "./components/ResetPasswordSuccessPage";
import ResetPasswordDebug from "./components/ResetPasswordDebug";

// Global set to track saved message IDs and prevent duplicates
const savedMessageIds = new Set<string>();
import UserManagementPage from "./components/UserManagementPage";

export type View = "chat" | "settings" | "userManagement";

function App() {
  // Initialize AI providers
  useEffect(() => {
    initializeProviders();
  }, []);

  const [showResetPassword, setShowResetPassword] = useState(false);
  const [resetPasswordError, setResetPasswordError] = useState<string | null>(
    null,
  );

  const handleForgotPassword = () => {
    console.log("[Auth Debug] Forgot Password triggered. Current auth state:", {
      isAuthenticated,
      authEmail,
      authError,
    });
    setShowSignInEmail(false);
    setShowResetPassword(true);
    setResetPasswordError(null);
  };

  const handleSendResetPassword = async (email: string): Promise<void> => {
    setResetPasswordError(null);
    console.log("[Auth Debug] Sending reset password for email:", email);
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    if (error) {
      console.log("[Auth Debug] Reset password error:", error.message);
      setResetPasswordError(error.message);
      throw new Error(error.message);
    }
    // Success - no error thrown
    console.log("[Auth Debug] Reset password email sent successfully.");
  };

  const handleBackToSignIn = () => {
    setShowResetPassword(false);
    setShowSignInEmail(true);
    setResetPasswordError(null);
  };
  const { t } = useTranslation();
  // UI and navigation state
  type Theme = "light" | "dark" | "system";
  type SettingsSubView = "main" | "account";
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 768);
  const [activeChatId, setActiveChatId] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<View>("chat");
  const [settingsSubView, setSettingsSubView] =
    useState<SettingsSubView>("main");
  // Auth modal flow state
  const [activeModal, setActiveModal] = useState<AuthView>(null);
  const [showSignInEmail, setShowSignInEmail] = useState(false);
  const [showSignUpEmail, setShowSignUpEmail] = useState(false);
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [authEmail, setAuthEmail] = useState("");
  const [authError, setAuthError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Close all auth modals when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setShowSignInEmail(false);
      setShowSignUpEmail(false);
      setShowEmailVerification(false);
      setActiveModal(null);
      setAuthEmail("");
      setAuthError(null);
    }
  }, [isAuthenticated]);

  // Open SignInModal from button
  const openSignInModal = useCallback(() => {
    setActiveModal("signIn");
    setShowSignInEmail(false);
    setShowSignUpEmail(false);
    setShowEmailVerification(false);
    setAuthEmail("");
    setAuthError(null);
  }, []);

  // Continue with Email from SignInModal
  const handleContinueWithEmail = () => {
    setShowSignInEmail(true);
    setActiveModal(null);
    setShowSignUpEmail(false);
    setShowEmailVerification(false);
    setAuthError(null);
  };

  // Go to SignUpEmailModal from SignInEmailModal
  const handleSignUpLink = () => {
    setShowSignInEmail(false);
    setShowSignUpEmail(true);
    setShowEmailVerification(false);
    setAuthError(null);
  };

  // Go to SignInEmailModal from SignUpEmailModal
  const handleSignInLink = () => {
    setShowSignInEmail(true);
    setShowSignUpEmail(false);
    setShowEmailVerification(false);
    setAuthError(null);
  };

  // Handle sign in with email/password
  const handleSignInEmail = async (email: string, password: string) => {
    setAuthError(null);
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) {
      setAuthError(error.message);
    } else {
      setShowSignInEmail(false);
      setAuthEmail("");
      setActiveModal(null);
    }
  };

  // Handle sign up with email/password
  const handleSignUpEmail = async (email: string, password: string) => {
    setAuthError(null);
    const { error } = await supabase.auth.signUp({ email, password });
    if (error) {
      setAuthError(error.message);
    } else {
      setShowSignUpEmail(false);
      setShowEmailVerification(true);
      setAuthEmail(email);
    }
  };

  // Close all auth modals
  const closeAllAuthModals = () => {
    setShowSignInEmail(false);
    setShowSignUpEmail(false);
    setShowEmailVerification(false);
    setShowResetPassword(false);
    setAuthEmail("");
    setAuthError(null);
    setActiveModal(null);
  };
  const [isAccountModalOpen, setIsAccountModalOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  const [userProfiles, setUserProfiles] = useState<UserProfiles | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [dataError, setDataError] = useState<string | null>(null);
  const [selectedAssistantId, setSelectedAssistantId] = useState<string | null>(
    null,
  );

  const [theme, setTheme] = useState<Theme>(() => {
    const storedTheme = localStorage.getItem("theme") as Theme | null;
    return storedTheme || "system";
  });

  useEffect(() => {
    // Helper function to apply the actual 'dark' or 'light' mode to the DOM
    const applyModeToDOM = (userPreference: Theme) => {
      let mode: "light" | "dark";
      if (userPreference === "system") {
        mode = window.matchMedia("(prefers-color-scheme: dark)").matches
          ? "dark"
          : "light";
      } else {
        mode = userPreference;
      }

      if (mode === "dark") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    };

    // Apply the theme when the component mounts or when the 'theme' state changes
    localStorage.setItem("theme", theme); // Save user's explicit preference
    applyModeToDOM(theme); // Apply to DOM based on this preference

    // Listener for system preference changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleSystemChange = () => {
      // Only re-apply if the user's current preference is 'system'
      if (theme === "system") {
        applyModeToDOM("system");
      }
    };

    mediaQuery.addEventListener("change", handleSystemChange);

    // Cleanup listener on component unmount or before effect re-runs
    return () => mediaQuery.removeEventListener("change", handleSystemChange);
  }, [theme]); // Re-run this effect when the user's theme preference changes

  // Initialize connection monitoring and auth state monitoring
  useEffect(() => {
    console.log(
      "🔍 [App] Starting connection monitoring and auth state monitoring",
    );

    // Initialize auth state monitoring for cache management
    userService.initializeAuthStateMonitoring();

    // Force an initial connection check
    connectionService.checkDatabaseConnection().then((isConnected) => {
      console.log(
        `🔍 [App] Initial connection check: ${isConnected ? "Connected" : "Not connected"}`,
      );
    });

    // Set up periodic connection checks
    const intervalId = setInterval(() => {
      connectionService.checkDatabaseConnection();
    }, 60000); // Check every minute

    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      console.log("🔍 [App] Starting initial data fetch...");
      setDataLoading(true);
      setDataError(null);

      // Test Supabase connection first
      const isConnected = await connectionService.checkDatabaseConnection();
      if (!isConnected) {
        console.error("❌ [App] Database connection check failed.");
        setDataError(
          "Database connection failed. Please check your internet connection.",
        );
        setDataLoading(false);
        return;
      }

      // Add a timeout to prevent infinite loading (increased to 30 seconds)
      let timeoutCleared = false;
      const timeoutId = setTimeout(() => {
        if (!timeoutCleared) {
          console.error("❌ [App] Data fetch timeout after 30 seconds");
          setDataError(
            "Data loading timed out. Please refresh the page or clear your browser cache.",
          );
          setDataLoading(false);
        }
      }, 30000);

      const clearTimeoutSafely = () => {
        timeoutCleared = true;
        clearTimeout(timeoutId);
      };

      try {
        // Check authentication status
        console.log("🔍 [App] Checking authentication status...");
        let isAuth;
        try {
          isAuth = await dataService.isAuthenticated();
          console.log("🔍 [App] Authentication status:", isAuth);
        } catch (err) {
          console.error("❌ [App] Error in dataService.isAuthenticated:", err);
          throw err;
        }
        setIsAuthenticated(isAuth);

        // Fetch data in parallel for better performance
        console.log(
          "🔍 [App] Fetching user data and chat sessions in parallel...",
        );
        const [userProfilesData, chatSessionsData] = await Promise.all([
          dataService.getUserProfiles().catch((err) => {
            console.error("❌ [App] Error fetching user profiles:", err);
            return null;
          }),
          dataService.getChatSessions().catch((err) => {
            console.error("❌ [App] Error fetching chat sessions:", err);
            return [];
          }),
        ]);

        console.log("🔍 [App] Data fetch results:", {
          userProfiles: !!userProfilesData,
          chatSessions: chatSessionsData?.length || 0,
        });

        if (userProfilesData) {
          setUserProfiles(userProfilesData);
          // Check if current user is admin
          const adminStatus = userProfilesData.loggedInUser?.role === "admin";
          console.log(
            "🔍 [App] Admin status:",
            adminStatus,
            "User role:",
            userProfilesData.loggedInUser?.role,
          );
          setIsAdmin(adminStatus);
        }
        setChatSessions(chatSessionsData || []);
        console.log("✅ [App] Initial data fetch completed successfully");
        clearTimeoutSafely();
        setDataError(null); // Clear any previous errors
      } catch (error) {
        console.error("❌ [App] Error fetching initial data:", error);
        setDataError(
          error instanceof Error ? error.message : "An unknown error occurred",
        );
        clearTimeoutSafely();
      } finally {
        console.log("🔍 [App] Setting dataLoading to false");
        setDataLoading(false);
      }
    };

    fetchData();

    // Listen for auth state changes with deduplication
    let isProcessingAuthChange = false;
    const {
      data: { subscription },
    } = dataService.onAuthStateChange(async (event, session) => {
      console.log("Auth state changed:", event, session?.user?.email);

      // Skip duplicate INITIAL_SESSION events
      if (event === "INITIAL_SESSION" && isProcessingAuthChange) {
        console.log("🔍 [App] Skipping duplicate INITIAL_SESSION event");
        return;
      }

      isProcessingAuthChange = true;
      setIsAuthenticated(!!session);

      if (session) {
        // Only fetch data if we don't already have it (avoid duplicate fetches)
        if (!userProfiles || !chatSessions.length) {
          console.log("🔍 [App] Auth change - Fetching data for new session");
          const [userProfilesData, chatSessionsData] = await Promise.all([
            dataService.getUserProfiles(),
            dataService.getChatSessions(),
          ]);

          if (userProfilesData) {
            setUserProfiles(userProfilesData);
            // Check if current user is admin
            const adminStatus = userProfilesData.loggedInUser?.role === "admin";
            console.log(
              "🔍 [App] Auth change - Admin status:",
              adminStatus,
              "User role:",
              userProfilesData.loggedInUser?.role,
            );
            setIsAdmin(adminStatus);
          }
          setChatSessions(chatSessionsData);
        } else {
          console.log(
            "🔍 [App] Auth change - Data already loaded, skipping fetch",
          );
        }
      } else {
        // User signed out, robustly clear all relevant state (do NOT fetch or set userProfiles/chatSessions)
        console.log("🔍 [App] User signed out - clearing all state and caches");

        // Clear service caches
        connectionService.clearAllCache();

        setIsAuthenticated(false);
        setIsAdmin(false);
        setUserProfiles(null);
        setChatSessions([]);
        setActiveChatId(null);
        setSelectedAssistantId(null); // Clear assistant selection
        resetChatSession();
        setCurrentView("chat");
        setActiveModal(null);
        setIsAccountModalOpen(false);
        setSettingsSubView("main");
        setShowSignInEmail(false);
        setShowSignUpEmail(false);
        setShowEmailVerification(false);
        setAuthEmail("");
        setAuthError(null);
        setIsSidebarOpen(false);
      }

      isProcessingAuthChange = false;
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const smallScreen = window.innerWidth < 768;
      setIsSmallScreen(smallScreen);
      if (isAuthenticated) {
        if (window.innerWidth >= 768) {
          setIsSidebarOpen(true);
        } else {
          setIsSidebarOpen(false);
        }
      }
    };
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, [isAuthenticated]);

  useEffect(() => {
    if (!isAuthenticated) {
      setIsSidebarOpen(false);
    } else {
      setIsSidebarOpen(window.innerWidth >= 768);
    }
  }, [isAuthenticated]);

  const handleLogin = useCallback(async () => {
    // Authentication state will be handled by the auth state change listener
    setActiveModal(null);
    setCurrentView("chat");
    setSettingsSubView("main");
  }, []);

  // Only clear cache and state on explicit logout
  const handleLogout = useCallback(async () => {
    try {
      await dataService.signOut();
      // Clear authentication state and caches only on logout
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserProfiles(null);
      setChatSessions([]);
      setActiveChatId(null);
      setSelectedAssistantId(null);
      resetChatSession();
      setCurrentView("chat");
      setActiveModal(null);
      setIsAccountModalOpen(false);
      setSettingsSubView("main");
      setShowSignInEmail(false);
      setShowSignUpEmail(false);
      setShowEmailVerification(false);
      setAuthEmail("");
      setAuthError(null);
      setIsSidebarOpen(false);
      // Optionally clear localStorage if needed
      // localStorage.clear();
      console.log("[Auth Debug] User logged out, cache and state cleared");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  }, []);

  const openChatHistoryAccessModal = useCallback(() => {
    setActiveModal("chatHistoryAccess");
  }, []);

  const closeModal = useCallback(() => {
    setActiveModal(null);
  }, []);

  const sidebarToggleHandler = useCallback(() => {
    if (!isAuthenticated) {
      openChatHistoryAccessModal();
    } else {
      setIsSidebarOpen((prev) => !prev);
    }
  }, [isAuthenticated, openChatHistoryAccessModal]);

  const handleSelectAssistant = useCallback(
    async (assistantId: string | null) => {
      console.log("[Assistant Debug] handleSelectAssistant called:", {
        assistantId,
        assistantIdType: typeof assistantId,
        currentSelectedAssistantId: selectedAssistantId,
      });

      setSelectedAssistantId(assistantId);
      console.log("[Assistant Debug] Selected assistant:", assistantId);

      // Clear active chat when selecting assistant (show welcome screen)
      setActiveChatId(null);

      // No longer create chat automatically - just update the assistant selection
      // Chat will be created when user sends their first message
    },
    [selectedAssistantId],
  );

  const navigateTo = useCallback(
    (view: View, subView: SettingsSubView = "main") => {
      setCurrentView(view);
      setSettingsSubView(subView);
      if (isAuthenticated && isSmallScreen) {
        if (view === "settings" || view === "chat") {
          setIsSidebarOpen(false);
        }
      }
    },
    [isSmallScreen, isAuthenticated],
  );

  const handleNewChat = useCallback(() => {
    // Start a new chat with no assistant context
    setActiveChatId(null);
    setSelectedAssistantId(null); // Clear assistant selection
    resetChatSession();
    setCurrentView("chat");
    setSettingsSubView("main");
    if (isAuthenticated && isSmallScreen) {
      setIsSidebarOpen(false);
    }
    console.log(
      "[Chat Debug] New chat started with no assistant (pure API mode)",
    );
  }, [isSmallScreen, isAuthenticated]);

  const handleSelectChat = useCallback(
    async (chatId: string) => {
      console.log("🔍 [handleSelectChat] Chat selected:", chatId);

      if (!isAuthenticated) {
        console.log(
          "🔍 [handleSelectChat] User not authenticated, opening modal",
        );
        openChatHistoryAccessModal();
        return;
      }

      resetChatSession();
      setActiveChatId(chatId);
      setCurrentView("chat");
      setSettingsSubView("main");
      if (isSmallScreen) {
        setIsSidebarOpen(false);
      }

      // Ensure database connection is healthy before loading messages
      const connectionReady = await connectionService.checkDatabaseConnection();
      if (!connectionReady) {
        console.log(
          "⚠️ [handleSelectChat] Database connection not ready, retrying...",
        );
        await connectionService.ensureConnection();
      }

      // Load messages for the selected chat if they haven't been loaded yet
      const selectedSession = chatSessions.find((s) => s.id === chatId);
      console.log(
        "🔍 [handleSelectChat] Selected session:",
        selectedSession?.title,
        "Messages count:",
        selectedSession?.messages.length,
      );

      // Restore assistant selection from chat session
      if (
        selectedSession?.assistantId &&
        selectedSession.assistantId !== selectedAssistantId
      ) {
        console.log(
          "[Assistant Debug] Restoring assistant selection from chat session:",
          {
            chatId,
            assistantId: selectedSession.assistantId,
            assistantName: selectedSession.assistantName,
            assistantEmoji: selectedSession.assistantEmoji,
          },
        );
        setSelectedAssistantId(selectedSession.assistantId);
      } else if (!selectedSession?.assistantId && selectedAssistantId) {
        // Clear assistant selection if chat has no assistant
        console.log(
          "[Assistant Debug] Clearing assistant selection for chat without assistant:",
          {
            chatId,
            selectedAssistantId,
          },
        );
        setSelectedAssistantId(null);
      }

      if (selectedSession && selectedSession.messages.length === 0) {
        try {
          console.log(
            "🔍 [handleSelectChat] Loading messages for chat:",
            chatId,
          );

          // Use retry logic for loading messages
          let retryCount = 0;
          let messages: any[] = [];

          while (retryCount < 3) {
            try {
              messages = await dataService.getMessagesForSession(chatId);
              break; // Success, exit retry loop
            } catch (err) {
              retryCount++;
              console.log(
                `⚠️ [handleSelectChat] Retry ${retryCount}/3 loading messages`,
              );
              await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait before retry
            }
          }

          if (messages.length > 0) {
            setChatSessions((prevSessions) =>
              prevSessions.map((session) =>
                session.id === chatId ? { ...session, messages } : session,
              ),
            );
            console.log(
              "✅ [handleSelectChat] Messages loaded for chat:",
              chatId,
              "Count:",
              messages.length,
            );
          } else {
            console.error(
              "❌ [handleSelectChat] Failed to load messages after retries",
            );
          }
        } catch (error) {
          console.error("❌ [handleSelectChat] Error loading messages:", error);
        }
      } else if (selectedSession) {
        console.log(
          "🔍 [handleSelectChat] Messages already loaded for this chat",
        );
      }
    },
    [
      isSmallScreen,
      isAuthenticated,
      openChatHistoryAccessModal,
      chatSessions,
      selectedAssistantId,
    ],
  );

  const handleEditChat = useCallback(
    async (chatId: string, newTitle: string) => {
      try {
        console.log(
          "🔍 [handleEditChat] Editing chat:",
          chatId,
          "New title:",
          newTitle,
        );

        const success = await dataService.updateChatTitle(chatId, newTitle);
        if (success) {
          setChatSessions((prevSessions) =>
            prevSessions.map((session) =>
              session.id === chatId ? { ...session, title: newTitle } : session,
            ),
          );
          console.log("✅ [handleEditChat] Chat title updated successfully");
        } else {
          console.error("❌ [handleEditChat] Failed to update chat title");
        }
      } catch (error) {
        console.error("❌ [handleEditChat] Error updating chat title:", error);
      }
    },
    [],
  );

  const handleDeleteChat = useCallback(
    async (chatId: string) => {
      try {
        console.log("🔍 [handleDeleteChat] Deleting chat:", chatId);

        const success = await dataService.deleteChatSession(chatId);
        if (success) {
          setChatSessions((prevSessions) =>
            prevSessions.filter((session) => session.id !== chatId),
          );

          // If the deleted chat was active, clear the active chat
          if (activeChatId === chatId) {
            setActiveChatId(null);
            resetChatSession();
          }

          console.log("✅ [handleDeleteChat] Chat deleted successfully");
        } else {
          console.error("❌ [handleDeleteChat] Failed to delete chat");
        }
      } catch (error) {
        console.error("❌ [handleDeleteChat] Error deleting chat:", error);
      }
    },
    [activeChatId],
  );

  // Utility to merge and deduplicate messages by id, preferring local unsaved messages if present
  const updateChatSession = useCallback(
    (
      chatId: string,
      newMessages: Message[],
      details?: {
        isNew?: boolean;
        title?: string;
        skipSave?: boolean;
        assistantId?: string | null;
        assistantName?: string | null;
        assistantEmoji?: string | null;
        mergeWithExisting?: boolean; // If true, merge with existing session messages
      },
    ) => {
      const now = new Date();
      const shouldSaveToDatabase = !details?.skipSave;

      // Find the previous session if merging is requested
      let mergedMessages = newMessages;
      if (details?.mergeWithExisting) {
        const prevSession = chatSessions.find((s) => s.id === chatId);
        if (prevSession) {
          mergedMessages = deduplicateMessages(
            newMessages,
            prevSession.messages,
          );
        }
      }

      console.log("[Assistant Debug] updateChatSession called:", {
        chatId,
        details,
        assistantContext: {
          assistantId: details?.assistantId,
          assistantName: details?.assistantName,
          assistantEmoji: details?.assistantEmoji,
        },
        messageCount: mergedMessages.length,
        savedMessageIdsCount: savedMessageIds.size,
      });

      // Check connection status before attempting to save
      if (shouldSaveToDatabase) {
        connectionService.checkDatabaseConnection().then((isConnected) => {
          if (!isConnected) {
            console.log(
              "⚠️ [updateChatSession] Database connection not ready, will ensure connection before saving",
            );
            connectionService.ensureConnection();
          }
        });
      }

      // Save to Supabase in background (non-blocking) only when needed
      if (shouldSaveToDatabase) {
        (async () => {
          try {
            // Check if user is authenticated
            const isAuth = await dataService.isAuthenticated();
            console.log("🔍 [updateChatSession] Authentication check:", isAuth);
            console.log(
              "🔍 [updateChatSession] Chat ID:",
              chatId,
              "Messages count:",
              mergedMessages.length,
            );

            if (isAuth) {
              // Only save messages that are not loading and not already saved
              const unsavedMessages = mergedMessages.filter(
                (msg) => !msg.isLoading && !savedMessageIds.has(msg.id),
              );
              for (const message of unsavedMessages) {
                try {
                  const savedMessage = await dataService.addMessage(
                    chatId,
                    message,
                  );
                  if (savedMessage) {
                    savedMessageIds.add(message.id);
                  }
                } catch (msgError) {
                  console.error("❌ Error saving message:", msgError);
                }
              }
            }
          } catch (error) {
            console.error("❌ Error in updateChatSession:", error);
          }
        })();
      }

      // Update local state (always update local state for UI responsiveness)
      console.log(
        "🔍 [updateChatSession] Updating local state for chat:",
        chatId,
        "isNew:",
        details?.isNew,
        "messages:",
        mergedMessages.length,
      );
      setChatSessions((prevSessions) => {
        if (details?.isNew) {
          const newSession: ChatSession = {
            id: chatId,
            title: details.title || `Chat ${now.toLocaleTimeString()}`,
            messages: mergedMessages,
            lastActivity: now,
            iconName: "SpeechBubbleIcon",
            assistantId: details.assistantId || null,
            assistantName: details.assistantName || null,
            assistantEmoji: details.assistantEmoji || null,
          };
          return [newSession, ...prevSessions.filter((s) => s.id !== chatId)];
        }
        const updatedSessions = prevSessions
          .map((session) => {
            if (session.id === chatId) {
              return {
                ...session,
                messages: mergedMessages,
                lastActivity: now,
              };
            }
            return session;
          })
          .sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());
        return updatedSessions;
      });
    },
    [chatSessions, setActiveChatId],
  );

  const openAccountModalHandler = () => {
    if (isSmallScreen) {
      navigateTo("settings", "account");
    } else {
      setIsAccountModalOpen(true);
    }
  };

  const closeAccountModalHandler = () => {
    if (isSmallScreen) {
      navigateTo("settings", "main");
    } else {
      setIsAccountModalOpen(false);
    }
  };

  const handleSaveAccountChanges = (updatedUserData: Partial<User>) => {
    if (userProfiles) {
      setUserProfiles((prevProfiles) => {
        if (!prevProfiles) return null;
        return {
          ...prevProfiles,
          loggedInUser: {
            ...prevProfiles.loggedInUser,
            ...updatedUserData,
          },
        };
      });
    }
    closeAccountModalHandler();
    console.log("Account changes saved (simulated):", updatedUserData);
  };

  const exportData = () => {
    const chatsToExport = chatSessions.map((session) => ({
      ...session,
      lastActivity: session.lastActivity.toISOString(),
      icon: undefined,
    }));
    const chatsJsonString = JSON.stringify(chatsToExport, null, 2);
    const chatsBlob = new Blob([chatsJsonString], { type: "application/json" });
    const chatsUrl = URL.createObjectURL(chatsBlob);
    const chatsLink = document.createElement("a");
    chatsLink.href = chatsUrl;
    chatsLink.download = "chats_updated.json";
    document.body.appendChild(chatsLink);
    chatsLink.click();
    document.body.removeChild(chatsLink);
    URL.revokeObjectURL(chatsUrl);

    if (userProfiles) {
      console.log(
        "Exporting userProfiles state:",
        JSON.parse(JSON.stringify(userProfiles)),
      );
      const usersJsonString = JSON.stringify(userProfiles, null, 2);
      const usersBlob = new Blob([usersJsonString], {
        type: "application/json",
      });
      const usersUrl = URL.createObjectURL(usersBlob);
      const usersLink = document.createElement("a");
      usersLink.href = usersUrl;
      usersLink.download = "users_updated.json";
      document.body.appendChild(usersLink);
      usersLink.click();
      document.body.removeChild(usersLink);
      URL.revokeObjectURL(usersUrl);
    }
  };

  const currentUser = getCurrentUser(isAuthenticated, userProfiles);

  // Debug: Log current state
  console.log("🔍 [App] Current state:", {
    isAuthenticated,
    isAdmin,
    currentUser,
    userRole: userProfiles?.loggedInUser?.role,
  });

  if (dataLoading) {
    return (
      <div
        className="flex items-center justify-center h-screen text-slate-700 dark:text-slate-300 text-lg bg-white dark:bg-slate-900"
        data-oid="olg09ng"
      >
        Loading application data...
      </div>
    );
  }

  // Temporarily bypass error screen for debugging - remove this in production
  const bypassErrorScreen = true;

  if (dataError && !bypassErrorScreen) {
    return (
      <div
        className="flex flex-col items-center justify-center h-screen text-red-600 dark:text-red-400 p-4 bg-white dark:bg-slate-900"
        data-oid="_---p-_"
      >
        <h1 className="text-xl font-semibold mb-2" data-oid="73au3p0">
          Error Loading Data
        </h1>
        <p className="text-center" data-oid="611se4q">
          {dataError}
        </p>
        <p
          className="mt-4 text-sm text-slate-500 dark:text-slate-400"
          data-oid="km6v_u-"
        >
          Please try refreshing the page or check the console for more details.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh Page
        </button>
      </div>
    );
  }

  const accountModalToShow =
    (currentView === "settings" &&
      settingsSubView === "account" &&
      isSmallScreen) ||
    (isAccountModalOpen && !isSmallScreen);

  return (
    <>
      <Routes data-oid="u_yf7nj">
        <Route
          path="/reset-password"
          element={<ResetPasswordPage data-oid="22kp0xf" />}
          data-oid="pu_ie9f"
        />

        <Route
          path="/reset-password-success"
          element={<ResetPasswordSuccessPage data-oid="eesvor1" />}
          data-oid="nn4jaz7"
        />

        <Route
          path="/reset-password-debug"
          element={<ResetPasswordDebug data-oid="n5g:1c1" />}
          data-oid="wvcgism"
        />

        <Route
          path="*"
          element={
            <div
              className="flex h-screen overflow-hidden bg-slate-100 dark:bg-slate-900"
              data-oid="bl23fe_"
            >
              {isAuthenticated &&
                isSmallScreen &&
                isSidebarOpen &&
                currentView === "chat" && (
                  <div
                    className="fixed inset-0 bg-black/50 dark:bg-black/70 z-40 transition-opacity duration-300 ease-in-out"
                    onClick={() => setIsSidebarOpen(false)}
                    aria-hidden="true"
                    data-oid="-b_-yg6"
                  />
                )}
              {/* ...existing code... (rest of App render remains unchanged) */}
              <Sidebar
                chatSessions={chatSessions}
                onNewChat={handleNewChat}
                onSelectChat={handleSelectChat}
                activeChatId={activeChatId}
                isSidebarOpen={isSidebarOpen}
                onToggleSidebar={sidebarToggleHandler}
                isSmallScreen={isSmallScreen}
                navigateTo={navigateTo}
                currentView={currentView}
                isAuthenticated={isAuthenticated}
                currentUser={currentUser}
                handleLogout={handleLogout}
                openSignInModal={openSignInModal}
                isAdmin={isAdmin}
                onEditChat={handleEditChat}
                onDeleteChat={handleDeleteChat}
                selectedAssistantId={selectedAssistantId}
                currentChatAssistantId={
                  activeChatId
                    ? chatSessions.find((s) => s.id === activeChatId)
                        ?.assistantId || null
                    : null
                }
                onSelectAssistant={handleSelectAssistant}
                data-oid="-cv8xcj"
              />

              {/* ...existing code... (rest of App render remains unchanged) */}
              {currentView === "chat" ? (
                <MainArea
                  activeChatId={activeChatId}
                  setActiveChatId={setActiveChatId}
                  chatMessages={
                    chatSessions.find((cs) => cs.id === activeChatId)
                      ?.messages || []
                  }
                  onUpdateChat={updateChatSession}
                  isSidebarOpen={isSidebarOpen}
                  onToggleSidebar={sidebarToggleHandler}
                  onNewChat={handleNewChat}
                  navigateTo={navigateTo}
                  isAuthenticated={isAuthenticated}
                  currentUser={currentUser}
                  handleLogout={handleLogout}
                  openSignInModal={openSignInModal}
                  selectedAssistantId={selectedAssistantId}
                  data-oid=".yolajm"
                />
              ) : currentView === "settings" && settingsSubView === "main" ? (
                <SettingsPage
                  navigateTo={navigateTo}
                  isSmallScreen={isSmallScreen}
                  openAccountModal={openAccountModalHandler}
                  settingsSubView={settingsSubView}
                  currentTheme={theme}
                  onThemeChange={setTheme}
                  data-oid="5v4:_ng"
                />
              ) : currentView === "userManagement" ? (
                <UserManagementPage
                  navigateTo={navigateTo}
                  data-oid="gomsaxt"
                />
              ) : null}
              {accountModalToShow && (
                <AccountModal
                  isOpen={true}
                  onClose={closeAccountModalHandler}
                  onSave={handleSaveAccountChanges}
                  currentUser={currentUser}
                  isSmallScreen={isSmallScreen}
                  data-oid="lggdk1r"
                />
              )}
              {activeModal === "signIn" && (
                <SignInModal
                  isOpen={true}
                  onClose={closeAllAuthModals}
                  onSignIn={handleLogin}
                  onContinueWithEmail={handleContinueWithEmail}
                  data-oid=":-1dvvs"
                />
              )}
              {showSignInEmail && (
                <SignInEmailModal
                  isOpen={true}
                  onClose={closeAllAuthModals}
                  onSignUp={handleSignUpLink}
                  onGoogleSignIn={() =>
                    supabase.auth.signInWithOAuth({ provider: "google" })
                  }
                  onGithubSignIn={() =>
                    supabase.auth.signInWithOAuth({ provider: "github" })
                  }
                  onSignIn={handleSignInEmail}
                  error={authError}
                  onForgotPassword={handleForgotPassword}
                  data-oid="w5h3w.b"
                />
              )}
              {showResetPassword && (
                <ResetPasswordModal
                  isOpen={true}
                  onClose={closeAllAuthModals}
                  onBackToSignIn={handleBackToSignIn}
                  onSendReset={handleSendResetPassword}
                  error={resetPasswordError}
                  data-oid="xrq.6wb"
                />
              )}
              {showSignUpEmail && (
                <SignUpEmailModal
                  isOpen={true}
                  onClose={closeAllAuthModals}
                  onSignIn={handleSignInLink}
                  onGoogleSignUp={() =>
                    supabase.auth.signInWithOAuth({ provider: "google" })
                  }
                  onGithubSignUp={() =>
                    supabase.auth.signInWithOAuth({ provider: "github" })
                  }
                  onSignUp={handleSignUpEmail}
                  error={authError}
                  data-oid="x76tkit"
                />
              )}
              {showEmailVerification && (
                <EmailVerificationModal
                  isOpen={true}
                  onClose={closeAllAuthModals}
                  email={authEmail}
                  data-oid="7x6wdwy"
                />
              )}
              {activeModal === "chatHistoryAccess" && (
                <ChatHistoryAccessModal
                  isOpen={true}
                  onClose={closeModal}
                  onSignInRedirect={() => {
                    closeModal();
                    openSignInModal();
                  }}
                  data-oid="_z0os:o"
                />
              )}
              <div
                className="fixed bottom-2 right-2 z-[100]"
                data-oid="zuxxe31"
              >
                <button
                  onClick={exportData}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow"
                  title={t(
                    "exportDataTooltip",
                    "Download chat sessions and user data as JSON",
                  )}
                  data-oid="s7uh5ek"
                >
                  {isAuthenticated
                    ? t("exportData")
                    : t("exportChatsFallback", "Export Chats")}
                </button>
              </div>
            </div>
          }
          data-oid="cx6sem1"
        />
      </Routes>
    </>
  );
}

export default App;
