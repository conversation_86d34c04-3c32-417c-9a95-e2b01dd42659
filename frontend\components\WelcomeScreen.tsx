import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Assistant } from "../types/assistant";
import { assistantService } from "../services/assistantService";
import { User } from "../types";

import { PASSelector } from "./PASSelector";
import { AssessmentModal, useAssessmentModal } from "./AssessmentModal";

interface WelcomeScreenProps {
  selectedAssistantId: string | null;
  currentUser: User | null;
  isAuthenticated: boolean;
  activeChatId?: string | null;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  selectedAssistantId,
  currentUser,
  isAuthenticated,
  activeChatId,
}) => {
  const { t } = useTranslation();
  const [selectedAssistant, setSelectedAssistant] = useState<Assistant | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(false);

  // Assessment modal state
  const {
    isOpen: isAssessmentOpen,
    assessmentId,
    openAssessment,
    closeAssessment,
  } = useAssessmentModal();

  // Handler for PAS assessment selection
  const handleSelectAssessment = (assessmentId: string) => {
    openAssessment(assessmentId);
  };

  // Handler for assessment completion
  const handleAssessmentComplete = (result: any) => {
    console.log("Assessment completed:", result);
  };

  // Handler for starting chat with assessment context
  const handleStartChatWithAssessment = (
    assessmentId: string,
    assessmentTitle: string,
  ) => {
    console.log("Starting chat with assessment context:", assessmentTitle);
  };

  // Load assistant details when selectedAssistantId changes
  useEffect(() => {
    const loadAssistant = async () => {
      if (selectedAssistantId) {
        setIsLoading(true);
        try {
          const assistant =
            await assistantService.getAssistant(selectedAssistantId);
          setSelectedAssistant(assistant);
        } catch (error) {
          console.error("Error loading assistant:", error);
          setSelectedAssistant(null);
        } finally {
          setIsLoading(false);
        }
      } else {
        setSelectedAssistant(null);
      }
    };

    loadAssistant();
  }, [selectedAssistantId]);

  // Get welcome message based on selected assistant
  const getWelcomeMessage = () => {
    if (!isAuthenticated) {
      return {
        greeting: t("welcome", "Welcome to AI Chat"),
        subtitle: t(
          "signInToStart",
          "Sign in to start chatting with AI assistants",
        ),
        emoji: "👋",
      };
    }

    const userName = currentUser?.name || "there";

    if (isLoading) {
      return {
        greeting: `Hi, ${userName}!`,
        subtitle: "Loading assistant...",
        emoji: "⏳",
      };
    }

    if (selectedAssistant) {
      // Custom welcome messages for each assistant
      const assistantWelcomes: Record<
        string,
        { greeting: string; subtitle: string }
      > = {
        "Default Assistant": {
          greeting: `Hi, ${userName}! I'm your AI Assistant.`,
          subtitle:
            "I'm here to help with general questions and tasks. What can I assist you with today?",
        },
        "Therapist Assistant": {
          greeting: `Hello, ${userName}. I'm your Therapist Assistant.`,
          subtitle:
            "I'm here to provide a safe space for you to explore your thoughts and feelings. How are you doing today?",
        },
        "Business Advisor": {
          greeting: `Good day, ${userName}! I'm your Business Advisor.`,
          subtitle:
            "I'm here to help with business strategy, entrepreneurship, and professional development. What business challenge can I help you tackle?",
        },
        "Learning Tutor": {
          greeting: `Hello, ${userName}! I'm your Learning Tutor.`,
          subtitle:
            "I'm excited to help you learn something new today! What subject or topic would you like to explore?",
        },
        "Code Assistant": {
          greeting: `Hey, ${userName}! I'm your Code Assistant.`,
          subtitle:
            "Ready to dive into some coding? I can help with programming questions, debugging, and best practices. What are you working on?",
        },
      };

      const welcome = assistantWelcomes[selectedAssistant.name] || {
        greeting: `Hi, ${userName}! I'm ${selectedAssistant.name}.`,
        subtitle: selectedAssistant.description,
      };

      return {
        ...welcome,
        emoji: selectedAssistant.emoji,
      };
    }

    // Default welcome when no assistant is selected
    return {
      greeting: `Hi, ${userName}! Welcome to AI Chat.`,
      subtitle:
        "Select an assistant from the sidebar to get started, or just start typing to chat with the default assistant.",
      emoji: "🤖",
    };
  };

  const { greeting, subtitle, emoji } = getWelcomeMessage();

  return (
    <div
      className="flex-1 flex items-center justify-center p-8"
      data-oid="l:f.t:y"
    >
      <div className="max-w-2xl mx-auto text-center" data-oid="eq:249p">
        {/* PASSelector: Assessment Cards */}
        <PASSelector
          onSelectAssessment={handleSelectAssessment}
          data-oid="zg37h7j"
        />

        {/* Assistant Emoji */}
        <div className="mb-6" data-oid="3p-lk55">
          <span className="text-6xl" data-oid="w96khrm">
            {emoji}
          </span>
        </div>

        {/* Greeting */}
        <h1
          className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-4"
          data-oid="bmgyctg"
        >
          {greeting}
        </h1>

        {/* Subtitle */}
        <p
          className="text-lg text-slate-600 dark:text-slate-400 mb-8 leading-relaxed"
          data-oid="idzb:oy"
        >
          {subtitle}
        </p>

        {/* Assistant Info (if selected) */}
        {selectedAssistant && isAuthenticated && (
          <div
            className="bg-slate-50 dark:bg-slate-800 rounded-lg p-6 mb-6"
            data-oid="owfga7n"
          >
            <div
              className="flex items-center justify-center mb-3"
              data-oid="uiwivjm"
            >
              <span className="text-2xl mr-3" data-oid="-ejuy0a">
                {selectedAssistant.emoji}
              </span>
              <h3
                className="text-xl font-semibold text-slate-900 dark:text-slate-100"
                data-oid="s7v56b."
              >
                {selectedAssistant.name}
              </h3>
            </div>
            <p
              className="text-slate-600 dark:text-slate-400"
              data-oid="4qxnnuc"
            >
              {selectedAssistant.description}
            </p>
          </div>
        )}

        {/* Call to Action */}
        {isAuthenticated && (
          <div
            className="text-slate-500 dark:text-slate-500 text-sm"
            data-oid="uwy_yk2"
          >
            <p data-oid="wes.jcx">
              💬 Start typing your message below to begin the conversation
            </p>
          </div>
        )}
      </div>

      {/* Assessment Modal */}
      <AssessmentModal
        isOpen={isAssessmentOpen}
        assessmentId={assessmentId}
        sessionId={activeChatId || ""}
        onClose={closeAssessment}
        onComplete={handleAssessmentComplete}
        onStartChat={handleStartChatWithAssessment}
        data-oid="ck60amg"
      />
    </div>
  );
};

export default WelcomeScreen;
