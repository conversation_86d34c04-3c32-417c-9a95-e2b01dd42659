import React from "react";
import { useTranslation } from "react-i18next";
import {
  CloseIcon,
  ZLogoIcon,
  GoogleIcon,
  EmailIcon,
  GithubIcon,
} from "../constants";

interface SignInModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSignIn: () => void;
  onContinueWithEmail: () => void;
}

const SignInModal: React.FC<SignInModalProps> = ({
  isOpen,
  onClose,
  onSignIn,
  onContinueWithEmail,
}) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-60 dark:bg-black/70 z-[70] flex items-center justify-center p-4 transition-opacity duration-300 ease-in-out"
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
      role="dialog"
      aria-modal="true"
      aria-labelledby="sign-in-dialog-title"
    >
      <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-md p-6 sm:p-8 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-1.5 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full"
          aria-label={t("closeSettings")}
        >
          <CloseIcon className="w-5 h-5" />
        </button>

        <div className="flex flex-col items-center text-center">
          <ZLogoIcon className="w-10 h-10 text-slate-800 dark:text-slate-200 mb-4" />

          <h1
            id="sign-in-dialog-title"
            className="text-2xl font-semibold text-slate-800 dark:text-slate-100 mb-2"
          >
            {t("signInModalTitle")}
          </h1>
          <p className="text-sm text-slate-500 dark:text-slate-400 mb-8">
            {t("signInModalDescription")}
          </p>

          <button
            onClick={onSignIn}
            className="w-full flex items-center justify-center bg-[#2A2B31] dark:bg-slate-700 hover:bg-opacity-90 dark:hover:bg-slate-600 text-white font-medium text-sm py-3 px-4 rounded-lg transition-colors"
            type="button"
          >
            <GoogleIcon className="w-5 h-5 mr-2.5" />
            {t("continueWithGoogle")}
          </button>

          <div className="flex items-center w-full my-4">
            <hr className="flex-grow border-t border-slate-200 dark:border-slate-700" />

            <span className="mx-3 text-xs text-slate-400 dark:text-slate-500 uppercase">
              {t("orSeparator")}
            </span>
            <hr className="flex-grow border-t border-slate-200 dark:border-slate-700" />
          </div>

          <button
            onClick={onContinueWithEmail}
            className="w-full flex items-center justify-center bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 font-medium text-sm py-3 px-4 rounded-lg mb-3 transition-colors"
            type="button"
          >
            <EmailIcon className="w-5 h-5 mr-2.5 text-slate-500 dark:text-slate-400" />

            {t("continueWithEmail")}
          </button>
          <button
            onClick={onSignIn}
            className="w-full flex items-center justify-center bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 font-medium text-sm py-3 px-4 rounded-lg mb-3 transition-colors"
          >
            <GithubIcon className="w-5 h-5 mr-2.5 text-slate-500 dark:text-slate-400" />

            {t("continueWithGithub")}
          </button>

          <button
            onClick={onClose}
            className="ButtonSkipForNow flex justify-center items-center  dark:text-gray-300 dark:hover:text-white w-full rounded-lg border border-black/10 dark:border-white/10 font-medium text-sm mb-3 py-2.5 dark:hover:bg-white/5"
          >
            {t("skipForNow")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SignInModal;
