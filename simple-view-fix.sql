-- Simple Security Definer View Fix
-- This script only fixes the security definer view issue

-- =============================================================================
-- 1. CHECK CURRENT VIEW DEFINITION
-- =============================================================================

-- First, let's see the current view definition
SELECT 
    schemaname,
    viewname,
    definition
FROM pg_views 
WHERE schemaname = 'public' 
    AND viewname = 'chat_sessions_with_stats';

-- =============================================================================
-- 2. DROP AND RECREATE VIEW WITHOUT SECURITY DEFINER
-- =============================================================================

-- Drop the existing view
DROP VIEW IF EXISTS public.chat_sessions_with_stats CASCADE;

-- Recreate the view with explicit security invoker
CREATE VIEW public.chat_sessions_with_stats 
WITH (security_invoker=true)
AS
SELECT 
    cs.*,
    COUNT(m.id) as message_count,
    MAX(m.timestamp) as last_message_at
FROM public.chat_sessions cs
LEFT JOIN public.messages m ON cs.id = m.chat_session_id
GROUP BY cs.id, cs.user_id, cs.title, cs.icon_name, cs.created_at, cs.updated_at, 
         cs.assistant_id, cs.assistant_name, cs.assistant_emoji, cs.assessment_id, 
         cs.assessment_title, cs.assessment_icon, cs.team_id;

-- =============================================================================
-- 3. VERIFY THE FIX
-- =============================================================================

-- Check if the view is now security invoker
SELECT 
    schemaname,
    viewname,
    CASE 
        WHEN definition LIKE '%SECURITY DEFINER%' THEN '❌ Still Security Definer'
        ELSE '✅ Security Invoker (Fixed)'
    END as status
FROM pg_views 
WHERE schemaname = 'public' 
    AND viewname = 'chat_sessions_with_stats';

-- Test the view works
SELECT COUNT(*) as view_test_count FROM public.chat_sessions_with_stats;

-- Success message
SELECT '🎉 Security definer view issue should be fixed!' as result;
