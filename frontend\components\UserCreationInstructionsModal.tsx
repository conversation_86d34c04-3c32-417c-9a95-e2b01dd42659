import React from "react";
import { useTranslation } from "react-i18next";
import { CloseIcon, ExternalLinkIcon } from "../constants";

interface UserCreationInstructionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    name: string;
    email: string;
    password: string;
    role: "admin" | "user";
  };
}

const UserCreationInstructionsModal: React.FC<
  UserCreationInstructionsModalProps
> = ({ isOpen, onClose, userData }) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  const openSupabaseDashboard = () => {
    // Open the Supabase dashboard - users will need to select their project
    window.open("https://supabase.com/dashboard/projects", "_blank");
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100">
            {t("createUserInstructions", "How to Create a New User")}
          </h3>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-500"
          >
            <CloseIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="space-y-4">
          <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              {t("userDetails", "User Details to Create")}
            </h4>
            <div className="space-y-2 text-sm">
              <div>
                <strong>{t("name")}:</strong> {userData.name}
              </div>
              <div>
                <strong>{t("email")}:</strong> {userData.email}
              </div>
              <div>
                <strong>{t("password")}:</strong> {userData.password}
              </div>
              <div>
                <strong>{t("role")}:</strong> {userData.role}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-medium text-slate-900 dark:text-slate-100">
              {t("followTheseSteps", "Follow these steps:")}
            </h4>

            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <div>
                  <p className="text-slate-700 dark:text-slate-300">
                    {t(
                      "goToSupabaseDashboard",
                      "Go to your Supabase project dashboard",
                    )}
                  </p>
                  <button
                    onClick={openSupabaseDashboard}
                    className="mt-2 inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                  >
                    {t("openDashboard", "Open Dashboard")}
                    <ExternalLinkIcon className="w-4 h-4 ml-1" />
                  </button>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <p className="text-slate-700 dark:text-slate-300">
                  {t(
                    "navigateToAuth",
                    "Navigate to Authentication → Users in the sidebar",
                  )}
                </p>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <p className="text-slate-700 dark:text-slate-300">
                  {t("clickAddUser", 'Click the "Add User" button')}
                </p>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  4
                </div>
                <div>
                  <p className="text-slate-700 dark:text-slate-300">
                    {t("enterUserDetails", "Enter the user details:")}
                  </p>
                  <ul className="mt-1 ml-4 text-sm text-slate-600 dark:text-slate-400 space-y-1">
                    <li>
                      • Email:{" "}
                      <code className="bg-slate-100 dark:bg-slate-700 px-1 rounded">
                        {userData.email}
                      </code>
                    </li>
                    <li>
                      • Password:{" "}
                      <code className="bg-slate-100 dark:bg-slate-700 px-1 rounded">
                        {userData.password}
                      </code>
                    </li>
                    <li>
                      • Check "Auto Confirm User" if you want to skip email
                      verification
                    </li>
                  </ul>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  5
                </div>
                <p className="text-slate-700 dark:text-slate-300">
                  {t("clickCreateUser", 'Click "Create User"')}
                </p>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  6
                </div>
                <div>
                  <p className="text-slate-700 dark:text-slate-300">
                    {t("updateProfile", "Update the user profile:")}
                  </p>
                  <ul className="mt-1 ml-4 text-sm text-slate-600 dark:text-slate-400 space-y-1">
                    <li>• Go to Table Editor → profiles</li>
                    <li>• Find the new user's row</li>
                    <li>
                      • Set name to:{" "}
                      <code className="bg-slate-100 dark:bg-slate-700 px-1 rounded">
                        {userData.name}
                      </code>
                    </li>
                    <li>
                      • Set role to:{" "}
                      <code className="bg-slate-100 dark:bg-slate-700 px-1 rounded">
                        {userData.role}
                      </code>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  ✓
                </div>
                <p className="text-slate-700 dark:text-slate-300">
                  {t(
                    "refreshUserList",
                    "Refresh this page to see the new user in the list",
                  )}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-amber-50 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
            <h4 className="font-medium text-amber-900 dark:text-amber-100 mb-2">
              {t("whyManualCreation", "Why Manual Creation?")}
            </h4>
            <p className="text-sm text-amber-800 dark:text-amber-200">
              {t(
                "securityExplanation",
                "For security reasons, user creation requires admin privileges that are not available in the browser. This ensures that only authorized administrators can create new users through the Supabase dashboard.",
              )}
            </p>
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            {t("understood", "Got it!")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserCreationInstructionsModal;
