import React, { useState, useEffect } from "react";
import Picker from "@emoji-mart/react";
import { Assistant } from "../types/assistant";
import { assistantService } from "../services/assistantService";

interface AssistantFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Assistant | null;
}

const defaultParams = [{ key: "", type: "string", value: "" }];

const AssistantFormModal: React.FC<AssistantFormModalProps> = ({
  isOpen,
  onClose,
  initialData,
}) => {
  const [emoji, setEmoji] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const emojiPickerRef = React.useRef<HTMLDivElement>(null);

  // Close picker on Esc or outside click
  useEffect(() => {
    if (!showEmojiPicker) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") setShowEmojiPicker(false);
    };
    const handleClick = (e: MouseEvent) => {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(e.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("mousedown", handleClick);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("mousedown", handleClick);
    };
  }, [showEmojiPicker]);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [instructions, setInstructions] = useState("");
  const [parameters, setParameters] = useState<any[]>(defaultParams);
  const [isSystem, setIsSystem] = useState(false);
  const [accessLevel, setAccessLevel] = useState<"free" | "premium">("free");
  const [error, setError] = useState("");

  useEffect(() => {
    if (initialData) {
      setEmoji(initialData.emoji || "");
      setName(initialData.name || "");
      setDescription(initialData.description || "");
      setInstructions(initialData.instructions || "");
      setParameters(
        initialData.parameters
          ? Object.entries(initialData.parameters).map(([key, value]) => ({
              key,
              type: typeof value,
              value,
            }))
          : defaultParams,
      );
      setIsSystem((initialData as any).is_system ?? false);
      setAccessLevel((initialData as any).access_level ?? "free");
    } else {
      setEmoji("");
      setName("");
      setDescription("");
      setInstructions("");
      setParameters(defaultParams);
      setIsSystem(false);
      setAccessLevel("free");
    }
    setError("");
  }, [initialData, isOpen]);

  const handleParamChange = (idx: number, field: string, value: string) => {
    setParameters((params) =>
      params.map((p, i) => (i === idx ? { ...p, [field]: value } : p)),
    );
  };

  const handleAddParam = () => {
    setParameters((params) => [
      ...params,
      { key: "", type: "string", value: "" },
    ]);
  };

  const handleRemoveParam = (idx: number) => {
    setParameters((params) => params.filter((_, i) => i !== idx));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name) {
      setError("Name is required.");
      return;
    }

    setError(""); // Clear any previous errors

    try {
      const paramObj: Record<string, any> = {};
      parameters.forEach((p) => {
        if (p.key) paramObj[p.key] = p.value;
      });

      if (initialData) {
        // Update existing assistant
        const updatedAssistant = {
          id: initialData.id,
          emoji,
          name,
          description,
          instructions,
          parameters: paramObj,
          is_system: isSystem,
          access_level: accessLevel,
        };
        await assistantService.updateAssistant(updatedAssistant);
      } else {
        // Create new assistant
        const newAssistant = {
          emoji,
          name,
          description,
          instructions,
          parameters: paramObj,
          is_system: isSystem,
          access_level: accessLevel,
        };
        await assistantService.createAssistant(newAssistant);
      }

      onClose();
    } catch (error) {
      console.error("Error saving assistant:", error);
      setError("Failed to save assistant. Please try again.");
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/30"
      data-oid="zx9hg45"
    >
      <div
        className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl w-full max-w-lg p-8 relative"
        data-oid="uxo4:7r"
      >
        <button
          className="absolute top-4 right-4 text-slate-400 hover:text-slate-700 dark:hover:text-white"
          onClick={onClose}
          aria-label="Close"
          data-oid="gghwqta"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            data-oid="85mtswn"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6 18L18 6M6 6l12 12"
              data-oid="wmf3ppv"
            />
          </svg>
        </button>
        <h2
          className="text-xl font-bold mb-6 text-slate-800 dark:text-white"
          data-oid="-8qrz.o"
        >
          {initialData ? "Edit Assistant" : "Add Assistant"}
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4" data-oid="u9.v-i:">
          <div className="flex gap-2 items-center" data-oid="fkuk3-.">
            <div
              className="relative flex items-center gap-2"
              data-oid="_5.e:m7"
            >
              <button
                type="button"
                className="px-1 py-1 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-xl"
                onClick={() => setShowEmojiPicker((v) => !v)}
                aria-label="Open Emoji Picker"
                data-oid="4z4nr.b"
              >
                {emoji || "😊"}
              </button>
              {showEmojiPicker && (
                <div
                  ref={emojiPickerRef}
                  className="absolute z-50 top-12 left-0"
                  data-oid="sfnt69p"
                >
                  <Picker
                    onEmojiSelect={(e: any) => {
                      setEmoji(e.native);
                      setShowEmojiPicker(false);
                    }}
                    title="Pick an emoji"
                    emoji="point_up"
                    style={{ position: "absolute" }}
                    data-oid="mik4czj"
                  />
                </div>
              )}
            </div>
            <input
              className="flex-1 px-3 py-2 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Name"
              required
              data-oid="kj8wjf3"
            />

            <select
              className="px-2 py-2 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
              value={isSystem ? "true" : "false"}
              onChange={(e) => setIsSystem(e.target.value === "true")}
              aria-label="Is System"
              data-oid="uq:5bdw"
            >
              <option value="true" data-oid="5denb.n">
                System
              </option>
              <option value="false" data-oid="b987hbx">
                User
              </option>
            </select>
            <select
              className="px-2 py-2 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
              value={accessLevel}
              onChange={(e) =>
                setAccessLevel(e.target.value as "free" | "premium")
              }
              aria-label="Access Level"
              data-oid="m96_nes"
            >
              <option value="free" data-oid="_59bvpa">
                Free
              </option>
              <option value="premium" data-oid="tzg9ydd">
                Premium
              </option>
            </select>
          </div>
          <textarea
            className="w-full px-3 py-2 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Description (optional)"
            rows={2}
            data-oid="e-efcp."
          />

          <textarea
            className="w-full px-3 py-2 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            placeholder="Instructions"
            rows={3}
            data-oid="h81d56f"
          />

          <div data-oid="608xdo9">
            <div
              className="font-medium mb-2 text-slate-700 dark:text-slate-200"
              data-oid="tk8o4.o"
            >
              Parameters
            </div>
            <div className="space-y-2" data-oid="5g-13zz">
              {parameters.map((p, idx) => (
                <div
                  key={idx}
                  className="flex gap-2 items-center"
                  data-oid="wyl671:"
                >
                  <input
                    className="w-24 px-2 py-1 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
                    value={p.key}
                    onChange={(e) =>
                      handleParamChange(idx, "key", e.target.value)
                    }
                    placeholder="Key"
                    data-oid="sqdayk4"
                  />

                  <select
                    className="px-2 py-1 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-slate-800 text-sm dark:text-white"
                    value={p.type}
                    onChange={(e) =>
                      handleParamChange(idx, "type", e.target.value)
                    }
                    data-oid="07.d4gi"
                  >
                    <option value="string" data-oid="6z8ym5m">
                      String
                    </option>
                    <option value="number" data-oid=".c49wyf">
                      Number
                    </option>
                    <option value="boolean" data-oid="6pusxia">
                      Boolean
                    </option>
                  </select>
                  <input
                    className="flex-1 px-2 py-1 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-slate-800 text-sm dark:text-white"
                    value={p.value}
                    onChange={(e) =>
                      handleParamChange(idx, "value", e.target.value)
                    }
                    placeholder="Value"
                    data-oid="4tht1rj"
                  />

                  <button
                    type="button"
                    className="text-red-500 hover:text-red-700 ml-1"
                    onClick={() => handleRemoveParam(idx)}
                    title="Remove"
                    data-oid="p9e:u-n"
                  >
                    🗑️
                  </button>
                </div>
              ))}
            </div>
            <button
              type="button"
              className="mt-2 px-3 py-1 rounded bg-slate-200 dark:bg-slate-700 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-300 dark:hover:bg-slate-600"
              onClick={handleAddParam}
              data-oid="1_x:wk:"
            >
              + Add Parameter
            </button>
          </div>
          {error && (
            <div className="text-red-500 text-sm" data-oid="m0bqn21">
              {error}
            </div>
          )}
          <div className="flex justify-end gap-2 mt-4" data-oid=".zs97b0">
            <button
              type="button"
              className="px-4 py-2 rounded bg-slate-200 dark:bg-slate-700 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-300 dark:hover:bg-slate-600"
              onClick={onClose}
              data-oid="x00gp2k"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 rounded bg-blue-600 text-sm text-white hover:bg-blue-700"
              data-oid="pln87_t"
            >
              {initialData ? "Save" : "Add"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssistantFormModal;
