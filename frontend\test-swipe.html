<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swipe Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .card-container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .card {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            transition: transform 0.3s ease-out;
            user-select: none;
            touch-action: pan-y;
        }
        
        .indicators {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 15px;
        }
        
        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ccc;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .indicator.active {
            background: #667eea;
        }
        
        .instructions {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Swipe Test</h1>
        <div class="instructions">
            Swipe left/right on the card below or use the indicators
        </div>
        
        <div class="card-container" id="cardContainer">
            <div class="card" data-index="0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">Card 1</div>
            <div class="card" data-index="1" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); transform: translateX(100%);">Card 2</div>
            <div class="card" data-index="2" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); transform: translateX(200%);">Card 3</div>
        </div>
        
        <div class="indicators">
            <div class="indicator active" data-index="0"></div>
            <div class="indicator" data-index="1"></div>
            <div class="indicator" data-index="2"></div>
        </div>
    </div>

    <script>
        class SwipeTest {
            constructor() {
                this.currentIndex = 0;
                this.totalCards = 3;
                this.isTransitioning = false;
                this.touchStartX = 0;
                this.touchStartY = 0;
                this.touchStartTime = 0;
                
                this.container = document.getElementById('cardContainer');
                this.cards = document.querySelectorAll('.card');
                this.indicators = document.querySelectorAll('.indicator');
                
                this.initEventListeners();
            }
            
            initEventListeners() {
                // Touch events
                this.container.addEventListener('touchstart', this.handleTouchStart.bind(this));
                this.container.addEventListener('touchmove', this.handleTouchMove.bind(this));
                this.container.addEventListener('touchend', this.handleTouchEnd.bind(this));
                
                // Mouse events for desktop testing
                this.container.addEventListener('mousedown', this.handleMouseDown.bind(this));
                this.container.addEventListener('mousemove', this.handleMouseMove.bind(this));
                this.container.addEventListener('mouseup', this.handleMouseUp.bind(this));
                
                // Indicator clicks
                this.indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', () => this.goToIndex(index));
                });
                
                // Keyboard navigation
                document.addEventListener('keydown', this.handleKeyDown.bind(this));
            }
            
            handleTouchStart(e) {
                this.touchStartX = e.touches[0].clientX;
                this.touchStartY = e.touches[0].clientY;
                this.touchStartTime = Date.now();
                e.preventDefault();
            }
            
            handleTouchMove(e) {
                if (!this.touchStartX) return;
                
                const deltaX = e.touches[0].clientX - this.touchStartX;
                const deltaY = e.touches[0].clientY - this.touchStartY;
                
                // Only handle horizontal swipes
                if (Math.abs(deltaX) > Math.abs(deltaY)) {
                    e.preventDefault();
                    this.updateCardPositions(deltaX);
                }
            }
            
            handleTouchEnd(e) {
                if (!this.touchStartX) return;
                
                const deltaX = e.changedTouches[0].clientX - this.touchStartX;
                const deltaY = e.changedTouches[0].clientY - this.touchStartY;
                const duration = Date.now() - this.touchStartTime;
                const velocity = Math.abs(deltaX) / duration;
                
                this.handleSwipeEnd(deltaX, deltaY, velocity);
                this.resetTouch();
            }
            
            handleMouseDown(e) {
                this.touchStartX = e.clientX;
                this.touchStartY = e.clientY;
                this.touchStartTime = Date.now();
                this.container.style.cursor = 'grabbing';
            }
            
            handleMouseMove(e) {
                if (!this.touchStartX) return;
                
                const deltaX = e.clientX - this.touchStartX;
                const deltaY = e.clientY - this.touchStartY;
                
                if (Math.abs(deltaX) > Math.abs(deltaY)) {
                    this.updateCardPositions(deltaX);
                }
            }
            
            handleMouseUp(e) {
                if (!this.touchStartX) return;
                
                const deltaX = e.clientX - this.touchStartX;
                const deltaY = e.clientY - this.touchStartY;
                const duration = Date.now() - this.touchStartTime;
                const velocity = Math.abs(deltaX) / duration;
                
                this.handleSwipeEnd(deltaX, deltaY, velocity);
                this.resetTouch();
                this.container.style.cursor = 'grab';
            }
            
            handleKeyDown(e) {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    this.goToPrevious();
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    this.goToNext();
                }
            }
            
            updateCardPositions(deltaX) {
                this.cards.forEach((card, index) => {
                    const offset = (index - this.currentIndex) * 100;
                    const adjustedDelta = deltaX * 0.3; // Damping factor
                    card.style.transform = `translateX(${offset + adjustedDelta}%)`;
                    card.style.transition = 'none';
                });
            }
            
            handleSwipeEnd(deltaX, deltaY, velocity) {
                const threshold = 50;
                const velocityThreshold = 0.3;
                
                // Reset positions first
                this.updateIndicators();
                this.updateCardPositions(0);
                
                // Check if it's a valid swipe
                if (Math.abs(deltaX) > Math.abs(deltaY) && 
                    Math.abs(deltaX) > threshold && 
                    velocity > velocityThreshold) {
                    
                    if (deltaX > 0) {
                        this.goToPrevious();
                    } else {
                        this.goToNext();
                    }
                } else {
                    // Snap back to current position
                    this.goToIndex(this.currentIndex);
                }
            }
            
            resetTouch() {
                this.touchStartX = 0;
                this.touchStartY = 0;
                this.touchStartTime = 0;
            }
            
            goToNext() {
                if (this.currentIndex < this.totalCards - 1 && !this.isTransitioning) {
                    this.goToIndex(this.currentIndex + 1);
                }
            }
            
            goToPrevious() {
                if (this.currentIndex > 0 && !this.isTransitioning) {
                    this.goToIndex(this.currentIndex - 1);
                }
            }
            
            goToIndex(index) {
                if (index === this.currentIndex || this.isTransitioning) return;
                
                this.isTransitioning = true;
                this.currentIndex = Math.max(0, Math.min(index, this.totalCards - 1));
                
                this.cards.forEach((card, cardIndex) => {
                    const offset = (cardIndex - this.currentIndex) * 100;
                    card.style.transform = `translateX(${offset}%)`;
                    card.style.transition = 'transform 0.3s ease-out';
                });
                
                this.updateIndicators();
                
                setTimeout(() => {
                    this.isTransitioning = false;
                }, 300);
            }
            
            updateIndicators() {
                this.indicators.forEach((indicator, index) => {
                    indicator.classList.toggle('active', index === this.currentIndex);
                });
            }
        }
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new SwipeTest();
        });
    </script>
</body>
</html>
