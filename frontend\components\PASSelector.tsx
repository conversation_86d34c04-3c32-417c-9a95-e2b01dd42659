import React, { useState, useEffect, useCallback, useRef } from "react";
import { PASCard } from "./PASCard";
import { assessmentService } from "../services/assessmentService";
import { Assessment } from "../types/assessment";

// Example assessment data; in production, fetch from API or context
const PAS_ASSESSMENTS = [
  {
    id: "6hn",
    title: "6 Human Needs",
    description: "Discover your core human needs and how they shape your life.",
    icon: "/assets/6hn-icon.png",
  },
  {
    id: "mbti",
    title: "MBTI",
    description: "Myers-Briggs Type Indicator personality assessment.",
    icon: "/assets/mbti-icon.png",
  },
  {
    id: "disc",
    title: "DiSC",
    description: "DiSC behavioral assessment for teams and individuals.",
    icon: "/assets/disc-icon.png",
  },
  // Add more as needed
];

interface PASSelectorProps {
  onSelectAssessment: (assessmentId: string) => void;
  selectedAssessmentId?: string;
  enableSwipe?: boolean; // New prop to enable/disable swiping
}

export const PASSelector: React.FC<PASSelectorProps> = ({
  onSelectAssessment,
  selectedAssessmentId,
  enableSwipe = true,
}) => {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDesktop, setIsDesktop] = useState(false);

  // Fetch assessments from database
  useEffect(() => {
    const fetchAssessments = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await assessmentService.getAssessments();
        setAssessments(response.assessments);
      } catch (err) {
        console.error("Error fetching assessments:", err);
        setError("Failed to load assessments");
        // Fallback to hardcoded assessments
        setAssessments(
          PAS_ASSESSMENTS.map((a) => ({ ...a, type: "6HN", is_active: true })),
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssessments();
  }, []);

  // Detect desktop/mobile for responsive behavior
  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 768); // sm breakpoint
    };

    checkIsDesktop();
    window.addEventListener("resize", checkIsDesktop);
    return () => window.removeEventListener("resize", checkIsDesktop);
  }, []);

  // Navigation functions with improved bounds checking
  const goToNext = useCallback(() => {
    const step = isDesktop ? 3 : 1; // Move by 3 cards on desktop, 1 on mobile
    const maxIndex = assessments.length - 1;

    if (currentIndex < maxIndex && !isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex((prev) => Math.min(prev + step, maxIndex));
      setTimeout(() => setIsTransitioning(false), 300);
    }
  }, [currentIndex, assessments.length, isTransitioning, isDesktop]);

  const goToPrevious = useCallback(() => {
    const step = isDesktop ? 3 : 1; // Move by 3 cards on desktop, 1 on mobile

    if (currentIndex > 0 && !isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex((prev) => Math.max(prev - step, 0));
      setTimeout(() => setIsTransitioning(false), 300);
    }
  }, [currentIndex, isTransitioning, isDesktop]);

  // Navigate to specific index with bounds checking
  const goToIndex = useCallback(
    (index: number) => {
      const clampedIndex = Math.max(0, Math.min(index, assessments.length - 1));
      if (clampedIndex !== currentIndex && !isTransitioning) {
        setIsTransitioning(true);
        setCurrentIndex(clampedIndex);
        setTimeout(() => setIsTransitioning(false), 300);
      }
    },
    [currentIndex, assessments.length, isTransitioning],
  );

  // Handle keyboard navigation
  useEffect(() => {
    if (!enableSwipe) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "ArrowLeft") {
        event.preventDefault();
        goToPrevious();
      } else if (event.key === "ArrowRight") {
        event.preventDefault();
        goToNext();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [enableSwipe, goToPrevious, goToNext]);

  // Calculate card positions and visibility with enhanced animations
  const getCardStyle = (index: number) => {
    if (!enableSwipe) {
      return {
        transform: "",
        transition: "",
        className: "",
        position: "static" as const,
      };
    }

    if (isDesktop) {
      // Desktop: Show 3 cards side by side as separate tiles
      const offset = index - currentIndex;
      const isVisible = offset >= 0 && offset <= 2; // Show current set of 3 cards

      return {
        transform: "",
        transition: isTransitioning ? "opacity 0.3s ease-out" : "",
        className: isVisible ? "opacity-100" : "opacity-0 pointer-events-none",
        zIndex: isVisible ? 10 : 1,
        position: "static" as const,
      };
    } else {
      // Mobile: Show 1 card at a time (original behavior)
      const offset = index - currentIndex;
      const translateX = offset * 100; // 100% width per card
      const isVisible = Math.abs(offset) <= 1; // Show current, previous, and next
      const isCurrent = offset === 0;
      const scale = isCurrent ? 1 : 0.9; // Slightly scale down non-current cards
      const zIndex = isCurrent ? 10 : Math.abs(offset) === 1 ? 5 : 1;

      return {
        transform: `translateX(${translateX}%) scale(${scale})`,
        transition: isTransitioning
          ? "transform 0.3s ease-out, opacity 0.3s ease-out"
          : "",
        className: isVisible ? "opacity-100" : "opacity-0 pointer-events-none",
        zIndex: zIndex,
        position: "absolute" as const,
      };
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div
        className="flex flex-col items-center w-full max-w-md mx-auto p-4"
        data-oid="c54t-ui"
      >
        <h2
          className="text-xl font-bold text-blue-700 dark:text-blue-200 mb-4"
          data-oid="ic8ev7h"
        >
          Personality Assessments
        </h2>
        <div
          className="flex items-center justify-center h-40"
          data-oid="sf5.82."
        >
          <div className="text-center" data-oid="zj2gy3e">
            <svg
              className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-2"
              fill="none"
              viewBox="0 0 24 24"
              data-oid="b-_u5ao"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
                data-oid="wxfkzto"
              />

              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                data-oid=":anitp_"
              />
            </svg>
            <p
              className="text-gray-600 dark:text-gray-400 text-sm"
              data-oid="v3inyw4"
            >
              Loading assessments...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className="flex flex-col items-center w-full max-w-md mx-auto p-4"
        data-oid="9x:rqa2"
      >
        <h2
          className="text-xl font-bold text-blue-700 dark:text-blue-200 mb-4"
          data-oid="f5wwtk5"
        >
          Personality Assessments
        </h2>
        <div
          className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"
          data-oid="fwnz12-"
        >
          <p
            className="text-red-700 dark:text-red-300 text-sm text-center"
            data-oid="p-hsw6c"
          >
            {error}
          </p>
        </div>
      </div>
    );
  }

  // No assessments state
  if (assessments.length === 0) {
    return (
      <div
        className="flex flex-col items-center w-full max-w-md mx-auto p-4"
        data-oid="ssois5o"
      >
        <h2
          className="text-xl font-bold text-blue-700 dark:text-blue-200 mb-4"
          data-oid="z1556z3"
        >
          Personality Assessments
        </h2>
        <div
          className="text-center h-40 flex items-center justify-center"
          data-oid="-r4naar"
        >
          <p
            className="text-gray-600 dark:text-gray-400 text-sm"
            data-oid="p.73k.h"
          >
            No assessments available
          </p>
        </div>
      </div>
    );
  }

  // Render different layouts based on enableSwipe
  if (!enableSwipe) {
    // Original grid layout for non-swipe mode
    return (
      <div
        className="flex flex-col items-center w-full max-w-3xl mx-auto"
        data-oid=":2ek1mv"
      >
        <h2
          className="text-xl font-bold text-blue-700 dark:text-blue-200 mb-2"
          data-oid="b3q6-99"
        >
          Personality Assessments
        </h2>
        <div
          className="flex flex-row flex-wrap justify-center gap-2"
          data-oid="r97ksem"
        >
          {assessments.map((a) => (
            <PASCard
              key={a.id}
              id={a.id}
              title={a.title}
              description={a.description}
              icon={a.icon}
              onSelect={onSelectAssessment}
              isSwipeable={false}
              data-oid="91utdu9"
            />
          ))}
        </div>
      </div>
    );
  }

  // Swipeable horizontal layout
  return (
    <div
      className={`flex flex-col items-center w-full mx-auto ${isDesktop ? "max-w-4xl" : "max-w-md"}`}
      data-oid="4hrnhh."
    >
      <h2
        className="text-xl font-bold text-blue-700 dark:text-blue-200 lg:mb-4 sm:mb-1"
        data-oid="a331tr-"
      >
        Personality Assessments
      </h2>
      {/* Swipe hint for mobile users */}
      <p
        className="text-sm text-gray-500 dark:text-gray-400 mb-2 sm:hidden"
        data-oid="4e:78gh"
      >
        Swipe left or right to explore
      </p>

      {/* Card container */}
      <div
        ref={containerRef}
        className={`w-full rounded-lg ${
          isDesktop
            ? "flex justify-center items-center gap-4 min-h-48"
            : "relative h-40 overflow-hidden"
        }`}
        style={isDesktop ? {} : { perspective: "1000px" }}
        data-oid="p6ex-xv"
      >
        {isDesktop
          ? // Desktop: Flex layout with visible cards side by side
            assessments
              .slice(currentIndex, currentIndex + 3)
              .map((assessment, relativeIndex) => {
                const actualIndex = currentIndex + relativeIndex;
                const style = getCardStyle(actualIndex);

                return (
                  <div
                    key={assessment.id}
                    className={`flex-1 max-w-sm transition-opacity duration-300 ${style.className}`}
                    style={{
                      transition: style.transition,
                      zIndex: style.zIndex,
                    }}
                    data-oid="iw8x12."
                  >
                    <PASCard
                      id={assessment.id}
                      title={assessment.title}
                      description={assessment.description}
                      icon={assessment.icon}
                      onSelect={onSelectAssessment}
                      onSwipeLeft={goToNext}
                      onSwipeRight={goToPrevious}
                      isSwipeable={true}
                      className="w-full"
                      data-oid="kus0pqh"
                    />
                  </div>
                );
              })
          : // Mobile: Absolute positioning with transforms (original behavior)
            assessments.map((assessment, index) => {
              const style = getCardStyle(index);
              return (
                <div
                  key={assessment.id}
                  className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${style.className}`}
                  style={{
                    transform: style.transform,
                    transition: style.transition,
                    zIndex: style.zIndex,
                    position: style.position,
                  }}
                  data-oid="x:28rc9"
                >
                  <PASCard
                    id={assessment.id}
                    title={assessment.title}
                    description={assessment.description}
                    icon={assessment.icon}
                    onSelect={onSelectAssessment}
                    onSwipeLeft={goToNext}
                    onSwipeRight={goToPrevious}
                    isSwipeable={true}
                    className="w-full max-w-xs mx-4"
                    data-oid="lbu:1cz"
                  />
                </div>
              );
            })}
      </div>

      {/* Navigation indicators */}
      <div className="flex space-x-2 mt-2" data-oid="3f1lm9c">
        {isDesktop
          ? // Desktop: Show indicators for sets of 3 cards
            Array.from(
              { length: Math.ceil(assessments.length / 3) },
              (_, setIndex) => (
                <button
                  key={setIndex}
                  className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                    Math.floor(currentIndex / 3) === setIndex
                      ? "bg-blue-600 dark:bg-blue-400"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}
                  onClick={() => goToIndex(setIndex * 3)}
                  aria-label={`Go to card set ${setIndex + 1}`}
                  data-oid="4pqups3"
                />
              ),
            )
          : // Mobile: Show indicators for individual cards
            assessments.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                  index === currentIndex
                    ? "bg-blue-600 dark:bg-blue-400"
                    : "bg-gray-300 dark:bg-gray-600"
                }`}
                onClick={() => goToIndex(index)}
                aria-label={`Go to assessment ${index + 1}`}
                data-oid="c572_w1"
              />
            ))}
      </div>

      {/* Navigation arrows for desktop */}
      <div
        className={`hidden sm:flex justify-between mt-2 ${isDesktop ? "w-full max-w-4xl" : "w-full max-w-xs"}`}
        data-oid="3cbhadd"
      >
        <button
          onClick={goToPrevious}
          disabled={currentIndex === 0 || isTransitioning}
          className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          aria-label={isDesktop ? "Previous card set" : "Previous assessment"}
          data-oid="23g:lys"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            data-oid="83kkg:z"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
              data-oid="bk6sv0d"
            />
          </svg>
        </button>

        <button
          onClick={goToNext}
          disabled={
            isDesktop
              ? currentIndex + 3 >= assessments.length || isTransitioning
              : currentIndex === assessments.length - 1 || isTransitioning
          }
          className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          aria-label={isDesktop ? "Next card set" : "Next assessment"}
          data-oid=".zl3g45"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            data-oid="t8allk-"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
              data-oid="v9rbfqg"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};
