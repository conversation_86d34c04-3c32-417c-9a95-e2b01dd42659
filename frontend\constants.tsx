import React from "react";
import { Model, User } from "./types";

// SVG Icons (existing icons remain unchanged, only data definitions below are modified or removed)
export const ZLogoIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 280 280" 
    fill="currentColor" 
    className={className || "w-6 h-6"}
  >
    <g transform="translate(0,280) scale(0.1,-0.1)">
      <path d="M1548 2605 c-94 -16 -186 -45 -272 -86 -136 -65 -112 -77 119 -61
361 26 650 3 862 -66 l53 -17 -36 -55 c-20 -30 -48 -67 -61 -82 -22 -23 -29
-25 -56 -17 -70 21 -218 21 -327 1 -58 -11 -106 -19 -107 -18 -1 1 18 26 42
56 56 70 57 91 3 130 -62 45 -83 40 -153 -33 -60 -64 -193 -247 -178 -247 4 0
-49 -28 -117 -63 -437 -218 -829 -602 -1016 -991 -94 -197 -123 -378 -80 -503
24 -72 90 -142 168 -181 109 -53 317 -74 474 -46 30 6 33 3 56 -48 20 -42 34
-59 64 -73 81 -40 234 -14 295 51 29 31 21 38 -24 19 -57 -24 -119 -20 -147
10 -12 13 -28 34 -34 47 -11 21 -8 23 155 64 420 108 761 249 1054 437 128 82
225 154 225 168 0 13 -9 11 -38 -10 -134 -94 -581 -266 -1007 -386 -119 -33
-405 -94 -405 -86 1 15 29 182 40 236 19 94 80 327 93 354 9 17 44 35 142 72
156 60 429 193 583 286 174 105 286 189 405 303 123 118 184 193 232 285 77
147 52 246 -85 346 -33 24 -60 49 -60 55 0 6 -7 17 -15 24 -12 10 -16 9 -21
-3 -4 -12 -19 -9 -88 19 -113 46 -295 90 -448 109 -147 18 -186 18 -290 0z
m895 -314 c41 -38 57 -73 57 -129 0 -143 -191 -334 -549 -549 -222 -133 -681
-357 -698 -340 -15 16 154 404 260 597 38 69 69 130 70 136 0 14 121 50 231
70 95 16 192 18 201 4 4 -6 -64 -78 -150 -162 -85 -83 -153 -153 -151 -156 9
-9 179 97 316 197 153 111 232 193 215 221 -3 5 15 34 40 65 25 31 53 67 61
81 16 24 18 24 43 8 14 -9 39 -28 54 -43z m-1051 -398 c-32 -64 -140 -320
-229 -542 l-58 -145 -80 -28 c-114 -39 -139 -38 -87 7 15 13 25 26 22 29 -9 8
-161 -32 -210 -57 -35 -17 -45 -27 -45 -47 0 -57 120 -76 270 -43 41 9 77 14
80 12 2 -3 -18 -72 -44 -154 -53 -163 -99 -341 -103 -397 -3 -34 -5 -36 -47
-42 -24 -3 -100 -6 -168 -6 -239 0 -379 56 -417 167 -21 61 -20 103 3 188 35
125 93 236 200 380 55 74 315 335 395 396 178 135 323 228 471 303 39 19 71
35 73 36 1 0 -11 -26 -26 -57z"/>
    </g>
  </svg>
);

export const GoogleIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className || "w-5 h-5"}
    viewBox="0 0 24 24"
    fill="currentColor"
    data-oid="2f1jq09"
  >
    <path
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      fill="#4285F4"
      data-oid="4e.iva2"
    />

    <path
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      fill="#34A853"
      data-oid="7:zlo8l"
    />

    <path
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      fill="#FBBC05"
      data-oid="hjoj2jd"
    />

    <path
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      fill="#EA4335"
      data-oid="6qggyaw"
    />

    <path d="M1 1h22v22H1z" fill="none" data-oid="gbhybbf" />
  </svg>
);

export const EmailIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="currentColor"
    className={className || "w-5 h-5"}
    data-oid="_6218up"
  >
    <path
      d="M3 4a2 2 0 00-2 2v8a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2H3zm12 2L10 9 5 6h10z"
      data-oid="2gcbv:j"
    />

    <path
      d="M5 11.5V7.889l5 2.5 5-2.5V11.5a.5.5 0 01-.5.5h-9a.5.5 0 01-.5-.5z"
      data-oid="4g2f9tx"
    />
  </svg>
);

export const SidebarToggleIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth="2"
    stroke="currentColor"
    className={className || "w-5 h-5 text-purple-400"}
    data-oid="dyej-zi"
  >
    <g opacity="0.8" data-oid="1qx.jzt">
      <path
        d="M3 17.0048V6.99666C3 4.78752 4.7865 3 6.99563 3H17.0037C19.2129 3 21 4.78757 21 6.99671V17.0048C21 19.214 19.2129 21 17.0037 21H6.99563C4.7865 21 3 19.2139 3 17.0048Z"
        strokeLinejoin="round"
        data-oid="dcjgabw"
      ></path>
      <path
        d="M15 3V21"
        strokeLinecap="round"
        strokeLinejoin="round"
        data-oid="7ymtco4"
      ></path>
      <path
        d="M8 10L10 12L8 14"
        strokeLinecap="round"
        strokeLinejoin="round"
        data-oid="wdfxolw"
      ></path>
    </g>
  </svg>
);

export const PencilIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="dg.n7rj"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
      data-oid=":nd01mp"
    />
  </svg>
);

export const SearchIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid=":jy6sr:"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
      data-oid="rf87nm9"
    />
  </svg>
);

export const ChevronDownIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-4 h-4"}
    data-oid="4x2cvmu"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m19.5 8.25-7.5 7.5-7.5-7.5"
      data-oid="itxuvtk"
    />
  </svg>
);

export const AiBotIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5 text-purple-600"}
    data-oid="pnwmmd4"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      data-oid="o6_c.19"
    />

    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.75 9.75a3 3 0 1 0-5.865 1.092A3.001 3.001 0 0 0 15.75 9.75Zm-.001 2.368a.75.75 0 0 1-.75-.75V10.5a.75.75 0 0 1 1.5 0v.868a.75.75 0 0 1-.75.75Z"
      data-oid="26pk:3b"
    />

    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9.75 14.25A2.25 2.25 0 0 0 12 16.5h0a2.25 2.25 0 0 0 2.25-2.25H9.75Z"
      data-oid="467nbo1"
    />
  </svg>
);

// To be removed after updating Sidebar
export const SpeechBubbleIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 32 32"
    className={className || "w-4 h-4 text-purple-400"}
    data-oid="vs2vi8n"
  >
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M25 7H7a4 4 0 00-4 4v10a4 4 0 004 4h11l6 4v-4h1a4 4 0 004-4V11a4 4 0 00-4-4z"
      data-oid="uh939e7"
    />

    <circle cx="9.5" cy="17.5" r="1.5" fill="currentColor" data-oid="6k_57if" />
    <circle
      cx="14.5"
      cy="17.5"
      r="1.5"
      fill="currentColor"
      data-oid="7tka0is"
    />

    <circle
      cx="19.5"
      cy="17.5"
      r="1.5"
      fill="currentColor"
      data-oid="g.f1_1p"
    />
  </svg>
);

export const FolderIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5 text-green-500"}
    data-oid="o.yn:qq"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"
      data-oid="p9a2rz2"
    />
  </svg>
);

export const CubeIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="6.vf8_6"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
      data-oid="k9a748p"
    />
  </svg>
);

export const GlobeAltIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="b.0-c4l"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A11.978 11.978 0 0 1 12 16.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253M3 12c0-.778.099-1.533.284-2.253M3 12c0 .778.099 1.533.284 2.253"
      data-oid="kg7di2h"
    />
  </svg>
);

export const ArrowUpIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid=".zr7o.f"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"
      data-oid="tg_pda_"
    />
  </svg>
);

export const ChartBarIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="avm1koj"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
      data-oid="l0in2qn"
    />
  </svg>
);

export const GamepadIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="v427:px"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M6 12H4.5m3 3V9M9 6H7.5m9 3h1.5m-3-3v6M15 6h1.5m-9 9.75A2.25 2.25 0 0 1 9.75 18h4.5a2.25 2.25 0 0 1 2.25-2.25V13.5A2.25 2.25 0 0 1 14.25 11.25H9.75A2.25 2.25 0 0 1 7.5 13.5v2.25Z"
      data-oid="khr5g9o"
    />

    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M19.5 13.5a2.25 2.25 0 0 1-2.25 2.25H15V9h2.25A2.25 2.25 0 0 1 19.5 11.25v2.25Z"
      data-oid="ds3k9kt"
    />

    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M4.5 13.5A2.25 2.25 0 0 0 6.75 15.75H9V9H6.75A2.25 2.25 0 0 0 4.5 11.25v2.25Z"
      data-oid="zp80:pb"
    />
  </svg>
);

export const MusicNoteIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="4ure5l_"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9 9V4.5M9 9c0-1.355.686-2.585 1.729-3.335A4.002 4.002 0 0 1 15 5.25c0 2.21-1.79 4-4 4H9Z"
      data-oid="es30lxs"
    />

    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9 14.25v-.01M9 14.25c-2.485 0-4.5 2.015-4.5 4.5S6.515 23.25 9 23.25s4.5-2.015 4.5-4.5M9 14.25S11.015 12 13.5 12s4.5 2.25 4.5 4.5V18"
      data-oid="pho:jzs"
    />
  </svg>
);

export const MoonIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="jh:72ic"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"
      data-oid="0jjrd-8"
    />
  </svg>
);

export const ClockIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="w6_5djn"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      data-oid="9b.9o:6"
    />
  </svg>
);

export const GithubIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    viewBox="0 0 16 16"
    fill="currentColor"
    className={className || "w-5 h-5"}
    data-oid="xely7sv"
  >
    <path
      d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"
      data-oid="vrjwvzj"
    ></path>
  </svg>
);

export const TwitterXIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className || "w-5 h-5"}
    data-oid="-olnbtj"
  >
    <path
      d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
      data-oid="y.axx-w"
    ></path>
  </svg>
);

export const DiscordIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    viewBox="0 0 245 240"
    fill="currentColor"
    className={className || "w-5 h-5"}
    data-oid="2lmslz_"
  >
    <path
      d="M104.4 103.9c-5.7 0-10.2 5-10.2 11.1s4.6 11.1 10.2 11.1c5.7 0 10.2-5 10.2-11.1.1-6.1-4.5-11.1-10.2-11.1zm36.7 0c-5.7 0-10.2 5-10.2 11.1s4.6 11.1 10.2 11.1c5.7 0 10.2-5 10.2-11.1s-4.5-11.1-10.2-11.1z"
      data-oid="tz7az_g"
    ></path>
    <path
      d="M189.5 20h-134C44.2 20 35 29.2 35 40.6v135.2c0 11.4 9.2 20.6 20.5 20.6h113.4l-5.3-18.5 12.8 11.9 12.1 11.2 21.5 19V40.6c0-11.4-9.2-20.6-20.5-20.6zm-38.6 130.6s-3.6-4.3-6.6-8.1c13.1-2.2 16.4-7.2 16.4-7.2-4.1 2.7-8 4.6-11.5 5.9-5 2.1-9.8 3.5-14.5 4.3-9.6 1.8-18.4 1.3-25.9-.1-5.7-1.1-10.6-2.7-14.7-4.3-2.3-.9-4.8-2-7.3-3.4-.3-.2-.6-.3-.9-.5-.2-.1-.3-.2-.4-.3-1.8-1-2.8-1.7-2.8-1.7s3.2 4.9 16.3 7.2c-3 3.8-6.7 8.1-6.7 8.1-22.1 0-29-12.3-29-12.3S81.2 138 97.6 138c0 0 5.7-7.2 14.3-13.3-6.2-2.3-13-4.9-13-16.9s0-16.9 13-16.9c13 0 13 16.9 13 16.9s7.8 2.5 13 4.9c8.6 6.1 14.3 13.3 14.3 13.3 16.4 0 20.1 10.3 20.1 10.3s-6.9 12.3-29 12.3z"
      data-oid="f.qwe3_"
    ></path>
  </svg>
);

export const BrainIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="0q2krsg"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      data-oid="3qk3ekx"
    />

    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9.75 9.75A2.25 2.25 0 0 1 12 7.5h0a2.25 2.25 0 0 1 2.25 2.25v.75A2.25 2.25 0 0 1 12 12.75h0A2.25 2.25 0 0 1 9.75 10.5v-.75Zm0 0H7.5m2.25 0V7.5M14.25 9.75H16.5m-2.25 0V7.5m0 2.25A2.25 2.25 0 0 0 12 12.75m0 0v2.25m0 0A2.25 2.25 0 0 1 9.75 17.25h0A2.25 2.25 0 0 1 7.5 15m4.5 0A2.25 2.25 0 0 0 14.25 17.25h0A2.25 2.25 0 0 0 16.5 15m-4.5 0H7.5m4.5 0H16.5"
      data-oid="5:u3gbc"
    />
  </svg>
);

export const SettingsIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth="1.5"
    stroke="currentColor"
    className={className || "w-6 h-6"}
    data-oid="sunxtv-"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 011.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.56.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.893.149c-.425.07-.765.383-.93.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 01-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.397.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 01-.12-1.45l.527-.737c.25-.35.273-.806.108-1.204-.165-.397-.505-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.107-1.204l-.527-.738a1.125 1.125 0 01.12-1.45l.773-.773a1.125 1.125 0 011.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894z"
      data-oid="8khlwsk"
    ></path>
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      data-oid="0:j2tyj"
    ></path>
  </svg>
);

export const UserCircleIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-6 h-6"}
    data-oid="qnrmpfd"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
      data-oid="9cajg41"
    />
  </svg>
);

export const ArchiveIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="2nw.rj2"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M20.25 7.5l-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"
      data-oid="b.echlb"
    />
  </svg>
);

export const SignOutIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="j.scrqz"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6A2.25 2.25 0 0 0 5.25 5.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"
      data-oid="3bb9f2i"
    />
  </svg>
);

export const UsersIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="1cil5cn"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
      data-oid="osv7v-5"
    />
  </svg>
);

export const PlusIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="fzx589u"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12 4.5v15m7.5-7.5h-15"
      data-oid="_6o7sja"
    />
  </svg>
);

export const TrashIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="rzf14jj"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
      data-oid="4h9j1bf"
    />
  </svg>
);

export const CloseIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-6 h-6"}
    data-oid="p9zvv8d"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M6 18L18 6M6 6l12 12"
      data-oid=":3_fsff"
    />
  </svg>
);

export const ExternalLinkIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-4 h-4"}
    data-oid="p.y-iw8"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"
      data-oid="1uxfd14"
    />
  </svg>
);

export const DotsVerticalIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="ljrun48"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
      data-oid="v-67qwl"
    />
  </svg>
);

export const UploadIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="w:4uqei"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
      data-oid="aculdm7"
    />
  </svg>
);

export const LockClosedIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="1-jfb6e"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"
      data-oid="rftu3fl"
    />
  </svg>
);

export const EyeIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="rvd8cdk"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"
      data-oid="j0y7ffz"
    />

    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15 12a3 3 0 11-6 0 3 3 0 0 1 6 0z"
      data-oid="08trn-v"
    />
  </svg>
);

export const EyeSlashIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="d9a:g4-"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243"
      data-oid="5sf95e_"
    />
  </svg>
);

export const EditMessageIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={2.3}
    stroke="currentColor"
    className={className || "w-4 h-4"}
    data-oid="5sn7gbj"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
      data-oid="bj2qazx"
    ></path>
  </svg>
);

export const CopyMessageIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 18 18"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-4 h-4"}
    data-oid="wwgv6rv"
  >
    <g id="å¤ åˆ¶_copy (6) 1" data-oid="m_yf-kf">
      <path
        id="Vector"
        d="M4.875 4.66161V2.92944C4.875 2.34696 5.3472 1.87476 5.92969 1.87476H15.0703C15.6528 1.87476 16.125 2.34696 16.125 2.92944V12.0701C16.125 12.6526 15.6528 13.1248 15.0703 13.1248H13.3186"
        strokeLinecap="round"
        strokeLinejoin="round"
        data-oid="lgk:_.m"
      ></path>
      <path
        id="Vector_2"
        d="M12.0703 4.87476H2.92969C2.3472 4.87476 1.875 5.34696 1.875 5.92944V15.0701C1.875 15.6526 2.3472 16.1248 2.92969 16.1248H12.0703C12.6528 16.1248 13.125 15.6526 13.125 15.0701V5.92944C13.125 5.34696 12.6528 4.87476 12.0703 4.87476Z"
        strokeLinejoin="round"
        data-oid=":.rre21"
      ></path>
    </g>
  </svg>
);

export const DeleteMessageIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={2}
    stroke="currentColor"
    className={className || "w-4 h-4"}
    data-oid="r2hgz.h"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
      data-oid="flcrod-"
    ></path>
  </svg>
);

export const GoodResponseIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    stroke="currentColor"
    fill="none"
    strokeWidth="2.3"
    viewBox="0 0 24 24"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className || "w-4 h-4"}
    xmlns="http://www.w3.org/2000/svg"
    data-oid=".dfikjh"
  >
    <path
      d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"
      data-oid="8z2y0qv"
    ></path>
  </svg>
);

export const BadResponseIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    stroke="currentColor"
    fill="none"
    strokeWidth="2.3"
    viewBox="0 0 24 24"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className || "w-4 h-4"}
    xmlns="http://www.w3.org/2000/svg"
    data-oid="x3_.q-0"
  >
    <path
      d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"
      data-oid="fl7qq:-"
    ></path>
  </svg>
);

export const RegenerateIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={2.3}
    stroke="currentColor"
    className={className || "w-4 h-4"}
    data-oid="1-4v9nq"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
      data-oid="j1l6gp6"
    ></path>
  </svg>
);

export const MODELS: Model[] = [
  { id: "gemini-2.0-flash", name: "Gemini 2.0 Flash" },
  { id: "mistral:mistral-large-latest", name: "Mistral Large Latest" },
  { id: "openrouter:z-ai/glm-4.5-air:free", name: "GLM 4.5 Air (OpenRouter)" },
  {
    id: "openrouter:mistralai/mistral-large",
    name: "Mistral Large (OpenRouter)",
  },
  {
    id: "openrouter:deepseek/deepseek-chat-v3-0324:free",
    name: "DeepSeek Chat V3 0324 (OpenRouter)",
  },
];

// UserProfiles interface removed from here

const defaultUser: User = { name: "", avatarUrl: "", email: "" };

// Assuming UserProfiles type will be imported from types.ts where needed.
// This function signature might need adjustment if UserProfiles type is used differently after the move.
// For now, let's keep it assuming it will be correctly typed in the calling context.
const defaultUserProfiles = {
  // : UserProfiles removed for now, will be inferred or explicitly typed in App.tsx
  loggedInUser: defaultUser,
  guestUser: defaultUser,
};

export const getCurrentUser = (
  isAuthenticated: boolean,
  userProfiles: { loggedInUser: User; guestUser: User } | null,
): User => {
  const profilesToUse = userProfiles || defaultUserProfiles;
  return isAuthenticated ? profilesToUse.loggedInUser : profilesToUse.guestUser;
};

export const SUGGESTION_CHIPS = [
  {
    text: "Data Visualization",
    icon: <ChartBarIcon data-oid="m1dde.9" />,
    type: "quick-replies",
  },
  {
    text: "Game Development",
    icon: <GamepadIcon data-oid="e8kmw:t" />,
    type: "quick-replies",
  },
  {
    text: "Audio Player",
    icon: <MusicNoteIcon data-oid=":p:lalg" />,
    type: "quick-replies",
  },
  {
    text: "Starry Night",
    icon: <MoonIcon data-oid="e16tyex" />,
    type: "quick-replies",
  },
  {
    text: "World Clock",
    icon: <ClockIcon data-oid="we6.w87" />,
    type: "quick-replies",
  },
];

export const CHAT_INPUT_BUTTONS = [
  {
    text: "Artifacts",
    icon: <CubeIcon data-oid="bsfn:yv" />,
    type: "chat-input",
  },
  {
    text: "Web Search",
    icon: <GlobeAltIcon data-oid="k7v:d3z" />,
    type: "chat-input",
  },
];

export const GEMINI_MODEL_NAME = "gemini-2.0-flash";

export const ICON_COMPONENTS: {
  [key: string]: React.FC<{ className?: string }>;
} = {
  AiBotIcon: AiBotIcon,
  SpeechBubbleIcon: SpeechBubbleIcon,
  FolderIcon: FolderIcon,
  CubeIcon: CubeIcon,
  UserCircleIcon: UserCircleIcon,
  UploadIcon: UploadIcon,
  LockClosedIcon: LockClosedIcon,
  EyeIcon: EyeIcon,
  EyeSlashIcon: EyeSlashIcon,
  EditMessageIcon: EditMessageIcon,
  CopyMessageIcon: CopyMessageIcon,
  DeleteMessageIcon: DeleteMessageIcon,
  GoodResponseIcon: GoodResponseIcon,
  BadResponseIcon: BadResponseIcon,
  RegenerateIcon: RegenerateIcon,
  DefaultChatIcon: SpeechBubbleIcon,
};
