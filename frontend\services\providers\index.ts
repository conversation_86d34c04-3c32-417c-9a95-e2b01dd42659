// services/providers/index.ts
import { unifiedStreamingService } from '../unifiedStreamingService';
import { MistralProvider } from './mistralProvider';
import { GeminiProvider } from './geminiProvider';
import { OpenRouterProvider } from './openRouterProvider';

// Register providers with unified streaming service
export function initializeProviders() {
  // Register Gemini provider
  unifiedStreamingService.registerProvider('gemini', new GeminiProvider({
    apiKey: import.meta.env.VITE_GEMINI_API_KEY || "DUMMY_KEY"
  }));

  // Register Mistral provider
  unifiedStreamingService.registerProvider('mistral', new MistralProvider({
    apiKey: import.meta.env.VITE_MISTRAL_API_KEY || "DUMMY_KEY"
  }));

  // Register OpenRouter provider
  unifiedStreamingService.registerProvider('openrouter', new OpenRouterProvider({
    apiKey: import.meta.env.VITE_OPENROUTER_API_KEY || "DUMMY_KEY"
  }));
} 