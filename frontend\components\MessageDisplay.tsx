import React, { useState } from "react";
import { Message, GroundingChunk } from "../types";
import { useTranslation } from "react-i18next";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { QuickAnswerButtons } from "./QuickAnswerButtons";
import { chatAssessmentService } from "../services/chatAssessmentService";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import {
  oneDark,
  oneLight,
} from "react-syntax-highlighter/dist/esm/styles/prism";
import {
  EditMessageIcon,
  CopyMessageIcon,
  DeleteMessageIcon,
  GoodResponseIcon,
  BadResponseIcon,
  RegenerateIcon,
} from "../constants";

type MessageDisplayProps = {
  messages: Message[];
  onAssessmentAnswer?: (
    questionId: string,
    quickAnswerId: string,
    answerText: string,
  ) => void;
  activeChatId?: string;
};

const MessageDisplay: React.FC<MessageDisplayProps> = ({
  messages,
  onAssessmentAnswer,
  activeChatId,
}) => {
  const { t } = useTranslation();
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Detect dark mode
  React.useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains("dark"));
    };

    checkDarkMode();

    // Watch for theme changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    });

    return () => observer.disconnect();
  }, []);

  // Handle assessment answer selection
  const handleAssessmentAnswer = async (
    questionId: string,
    quickAnswerId: string,
    answerText: string,
  ) => {
    if (!activeChatId || !onAssessmentAnswer) return;

    try {
      // For regular assessment questions, delegate to parent handler
      console.log(
        "🔄 [MessageDisplay.handleAssessmentAnswer] Regular mode - calling parent handler",
      );
      onAssessmentAnswer(questionId, quickAnswerId, answerText);
    } catch (error) {
      console.error("Error handling assessment answer:", error);
    }
  };

  // Custom code block component with copy functionality
  const CodeBlock = ({ node, inline, className, children, ...props }: any) => {
    const [copied, setCopied] = useState(false);
    const match = /language-(\w+)/.exec(className || "");
    const language = match ? match[1] : "";

    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(
          String(children).replace(/\n$/, ""),
        );
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error("Failed to copy text: ", err);
      }
    };

    if (inline) {
      return (
        <code
          className="px-1.5 py-0.5 text-sm bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-200 rounded border border-slate-200 dark:border-slate-700 font-mono"
          {...props}
          data-oid="qan5ph9"
        >
          {children}
        </code>
      );
    }

    return (
      <div className="relative group my-4" data-oid="uuxmybj">
        <div
          className="flex items-center justify-between bg-slate-100 dark:bg-slate-800 px-4 py-2 rounded-t-lg border border-slate-200 dark:border-slate-700"
          data-oid="on.yl_i"
        >
          <span
            className="text-xs font-medium text-slate-600 dark:text-slate-400 uppercase"
            data-oid="-wrf1-7"
          >
            {language || "code"}
          </span>
          <button
            onClick={copyToClipboard}
            className="flex items-center gap-1 px-2 py-1 text-xs bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600 text-slate-600 dark:text-slate-300 rounded transition-colors"
            data-oid="_wc7upc"
          >
            {copied ? (
              <>
                <svg
                  className="w-3 h-3"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  data-oid="m94x9q3"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                    data-oid="0l16.y."
                  />
                </svg>
                {t("copied", "Copied")}
              </>
            ) : (
              <>
                <svg
                  className="w-3 h-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  data-oid="3ocfxvf"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    data-oid="t5__w_4"
                  />
                </svg>
                {t("copy", "Copy")}
              </>
            )}
          </button>
        </div>
        <SyntaxHighlighter
          style={isDarkMode ? oneDark : oneLight}
          language={language}
          PreTag="div"
          customStyle={{
            margin: 0,
            borderRadius: "0 0 0.5rem 0.5rem",
            border: "1px solid",
            borderColor: isDarkMode ? "#374151" : "#e2e8f0",
            borderTop: "none",
          }}
          codeTagProps={{
            style: {
              fontSize: "0.875rem",
              fontFamily:
                'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
            },
          }}
          {...props}
          data-oid="b5-3wfd"
        >
          {String(children).replace(/\n$/, "")}
        </SyntaxHighlighter>
      </div>
    );
  };

  // Custom markdown components
  const markdownComponents = {
    code: CodeBlock,
    pre: ({ children }: any) => <>{children}</>,
    h1: ({ children }: any) => (
      <h1
        className="text-2xl font-bold mt-6 mb-4 text-slate-900 dark:text-slate-100 border-b border-slate-200 dark:border-slate-700 pb-2"
        data-oid="y.kmx22"
      >
        {children}
      </h1>
    ),

    h2: ({ children }: any) => (
      <h2
        className="text-xl font-semibold mt-5 mb-3 text-slate-900 dark:text-slate-100"
        data-oid="z50iq:1"
      >
        {children}
      </h2>
    ),

    h3: ({ children }: any) => (
      <h3
        className="text-lg font-medium mt-4 mb-2 text-slate-900 dark:text-slate-100"
        data-oid="mh417.f"
      >
        {children}
      </h3>
    ),

    p: ({ children }: any) => {
      // Always use div to avoid nesting issues with code blocks and other block elements
      return (
        <div
          className="mb-4 text-slate-700 dark:text-slate-300 leading-relaxed"
          data-oid="r83ueu."
        >
          {children}
        </div>
      );
    },
    ul: ({ children }: any) => (
      <ul
        className="mb-4 ml-6 space-y-1 list-disc text-slate-700 dark:text-slate-300"
        data-oid="pngma:8"
      >
        {children}
      </ul>
    ),

    ol: ({ children }: any) => (
      <ol
        className="mb-4 ml-6 space-y-1 list-decimal text-slate-700 dark:text-slate-300"
        data-oid="3a1c3dg"
      >
        {children}
      </ol>
    ),

    li: ({ children }: any) => (
      <li className="leading-relaxed" data-oid="-m64ijf">
        {children}
      </li>
    ),

    blockquote: ({ children }: any) => (
      <blockquote
        className="border-l-4 border-slate-300 dark:border-slate-600 pl-4 my-4 italic text-slate-600 dark:text-slate-400"
        data-oid="5ffn55s"
      >
        {children}
      </blockquote>
    ),

    a: ({ href, children }: any) => (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
        data-oid="daowz5v"
      >
        {children}
      </a>
    ),

    table: ({ children }: any) => (
      <div className="overflow-x-auto my-4" data-oid="c-r4nlp">
        <table
          className="min-w-full border border-slate-200 dark:border-slate-700 rounded-lg"
          data-oid="fj9p..7"
        >
          {children}
        </table>
      </div>
    ),

    thead: ({ children }: any) => (
      <thead className="bg-slate-50 dark:bg-slate-800" data-oid="nixda.8">
        {children}
      </thead>
    ),

    th: ({ children }: any) => (
      <th
        className="px-4 py-2 text-left font-medium text-slate-900 dark:text-slate-100 border-b border-slate-200 dark:border-slate-700"
        data-oid="4.eaifw"
      >
        {children}
      </th>
    ),

    td: ({ children }: any) => (
      <td
        className="px-4 py-2 text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700"
        data-oid="qt.g8j3"
      >
        {children}
      </td>
    ),
  };

  const formatTimestamp = (timestamp: Date) => {
    try {
      // Handle case where timestamp might be a string or invalid date
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return "Invalid time";
      }

      return new Intl.DateTimeFormat("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }).format(date);
    } catch (error) {
      console.warn("Error formatting timestamp:", error);
      return "Invalid time";
    }
  };

  const renderGroundingChunks = (groundingChunks?: GroundingChunk[]) => {
    if (!groundingChunks || groundingChunks.length === 0) return null;

    return (
      <div className="mt-3 space-y-2" data-oid="7wiu.g4">
        <div
          className="text-xs text-slate-500 dark:text-slate-400 font-medium"
          data-oid="_5a:zl:"
        >
          {t("sources")}:
        </div>
        <div className="flex flex-wrap gap-2" data-oid="_7ul472">
          {groundingChunks.map((chunk, index) => {
            const source = chunk.web || chunk.retrievedContext;
            if (!source) return null;

            return (
              <a
                key={index}
                href={source.uri}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-2 py-1 text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
                data-oid="i8vpznz"
              >
                <span className="truncate max-w-[200px]" data-oid="ifbaw-t">
                  {source.title}
                </span>
                <svg
                  className="w-3 h-3 ml-1 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  data-oid="kc77a5x"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    data-oid="3fc9a.r"
                  />
                </svg>
              </a>
            );
          })}
        </div>
      </div>
    );
  };

  // Message rendering function
  const renderMessage = (message: Message) => {
    const isUser = message.sender === "user";
    const isAI = message.sender === "ai";

    return (
      <div
        key={message.id}
        data-message-id={message.id}
        className={`message-container group flex mb-6 md:mb-8 lg:mb-10 ${isUser ? "justify-end" : ""}`}
        data-oid="uqdt3x5"
      >
        {/* Only show AI avatar */}
        {isAI && (
          <div
            className="flex-shrink-0 flex flex-col items-center justify-start mr-3"
            data-oid="un0kiop"
          >
            <div
              className="w-8 h-8 rounded-full bg-slate-600 dark:bg-slate-400 flex items-center justify-center"
              data-oid="_y5j9q2"
            >
              <svg
                className="w-5 h-5 text-white dark:text-slate-800"
                fill="currentColor"
                viewBox="0 0 24 24"
                data-oid="n.9cd0t"
              >
                <path
                  d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                  data-oid="r2wskqk"
                />
              </svg>
            </div>
          </div>
        )}

        {/* Message bubble and content */}
        <div
          className={`flex flex-col ${isUser ? "items-end" : "items-start"} w-full max-w-full`}
          data-oid="s.82llj"
        >
          <div className="relative group w-full" data-oid="x1c97l4">
            {/* User bubble right-aligned wrapper */}
            {isUser ? (
              <div className="w-full flex justify-end" data-oid="ts:x-ov">
                <div
                  className={`rounded-2xl min-w-0 overflow-hidden bg-white dark:bg-slate-700 text-slate-800 dark:text-white border shadow-sm border-slate-200 dark:border-slate-600 user-bubble message-content rounded-br-md mb-[8px]`}
                  style={{
                    display: "inline-block",
                    minWidth: "2.5rem",
                    padding: "10px 20px",
                  }}
                  data-oid="8jyhl5n"
                >
                  {message.isLoading ? (
                    <div
                      className="flex items-center space-x-2"
                      data-oid="bo1a4-."
                    >
                      <div className="flex space-x-1" data-oid="36-skbz">
                        <div
                          className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"
                          data-oid="8_wfjsz"
                        ></div>
                        <div
                          className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                          data-oid="51.d7p0"
                        ></div>
                        <div
                          className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                          data-oid="rt_qasn"
                        ></div>
                      </div>
                      <span
                        className="text-sm text-slate-500 dark:text-slate-400"
                        data-oid="spz4jg7"
                      >
                        {t("thinking")}
                      </span>
                    </div>
                  ) : (
                    <div className="markdown-content" data-oid="o2dx7s1">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={markdownComponents}
                        data-oid="fhrbu-8"
                      >
                        {message.text}
                      </ReactMarkdown>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div
                className={`rounded-2xl min-w-0 overflow-hidden ${
                  message.isError
                    ? "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 message-content rounded-bl-md mr-0"
                    : "bg-[#F8FAFC] dark:bg-[#0F172A] text-slate-800 dark:text-slate-200 message-content rounded-bl-md ml-0"
                }`}
                style={{
                  width: "100%",
                  maxWidth: "700px",
                  minWidth: "2.5rem",
                }}
                data-oid="_maaqjm"
              >
                <div className="p-4" data-oid="1czjrts">
                  {message.isLoading && (
                    <div
                      className="flex items-center space-x-2 mb-3"
                      data-oid="yk5mzsi"
                    >
                      <div className="flex space-x-1" data-oid="c_i4:rx">
                        <div
                          className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"
                          data-oid="--s.jin"
                        ></div>
                        <div
                          className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                          data-oid="sk91:0i"
                        ></div>
                        <div
                          className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                          data-oid="gbv4z34"
                        ></div>
                      </div>
                      <span
                        className="text-sm text-slate-500 dark:text-slate-400"
                        data-oid="e_qxg3f"
                      >
                        {t("thinking")}
                      </span>
                    </div>
                  )}
                  {message.text && (
                    <div className="markdown-content" data-oid="vpanh0k">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={markdownComponents}
                        data-oid="chqro4-"
                      >
                        {message.text}
                      </ReactMarkdown>
                    </div>
                  )}
                  {message.isLoading && (
                    <span
                      className="inline-block w-0.5 h-4 bg-slate-600 dark:bg-slate-400 animate-pulse ml-1"
                      data-oid="rcpriyd"
                    />
                  )}
                  {/* Grounding chunks for AI messages */}
                  {isAI &&
                    !message.isLoading &&
                    renderGroundingChunks(message.groundingChunks)}

                  {/* Assessment Quick Answer Buttons */}
                  {isAI &&
                    !message.isLoading &&
                    message.assessmentData?.type === "assessment_question" &&
                    message.assessmentData.quickAnswers &&
                    message.assessmentData.questionId &&
                    onAssessmentAnswer && (
                      <QuickAnswerButtons
                        questionId={message.assessmentData.questionId}
                        quickAnswers={message.assessmentData.quickAnswers}
                        onAnswerSelect={handleAssessmentAnswer}
                        data-oid="0whdyq3"
                      />
                    )}
                </div>
              </div>
            )}
          </div>
          {/* Timestamp and Action Icons */}
          <div
            className={`flex items-center gap-2 w-full text-xs text-slate-500 dark:text-slate-400 ${isUser ? "justify-end mt-1" : "justify-start mt-1"}`}
            data-oid="aa6o2no"
          >
            <div
              className="relative min-w-[80px] flex items-center"
              data-oid="hmi.z:i"
            >
              <span
                className={`transition-opacity duration-200 ${isUser ? "text-right" : "text-left"} group-hover:opacity-0 opacity-100`}
                data-oid="xbq11ol"
              >
                {formatTimestamp(message.timestamp)}
              </span>
              <div
                className={`absolute left-0 right-0 flex gap-1 transition-opacity duration-200 opacity-0 group-hover:opacity-100 ${isUser ? "justify-end" : "justify-start"}`}
                data-oid="chmv5d4"
              >
                {isUser && !message.isLoading && (
                  <>
                    <button
                      title="Edit"
                      className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"
                      data-oid="8z55l8l"
                    >
                      <EditMessageIcon className="w-4 h-4" data-oid="0b.v1du" />
                    </button>
                    <button
                      title="Copy"
                      className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"
                      data-oid="8v1.5co"
                    >
                      <CopyMessageIcon className="w-4 h-4" data-oid="fg5vfwo" />
                    </button>
                    <button
                      title="Delete"
                      className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                      data-oid="-zorjln"
                    >
                      <DeleteMessageIcon
                        className="w-4 h-4"
                        data-oid="wd1qb8-"
                      />
                    </button>
                  </>
                )}
                {isAI && !message.isLoading && (
                  <>
                    <button
                      title="Good response"
                      className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 transition-colors"
                      data-oid="8q8pwrn"
                    >
                      <GoodResponseIcon
                        className="w-4 h-4"
                        data-oid="eas6gq_"
                      />
                    </button>
                    <button
                      title="Bad response"
                      className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-red-500 dark:text-red-400 hover:text-red-700 dark:text-red-300 transition-colors"
                      data-oid="1k7qyvp"
                    >
                      <BadResponseIcon className="w-4 h-4" data-oid="ggm63er" />
                    </button>
                    <button
                      title="Regenerate"
                      className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"
                      data-oid="l979yem"
                    >
                      <RegenerateIcon className="w-4 h-4" data-oid="9jb:8qa" />
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (messages.length === 0) {
    return null;
  }

  return (
    <div
      className="w-full max-w-3xl mx-auto message-scrollbar"
      data-oid="naj-ntk"
    >
      <div className="flex flex-col space-y-4 p-6" data-oid="psm3xvu">
        {messages.map(renderMessage)}
      </div>
    </div>
  );
};

export default MessageDisplay;
