import { supabase } from '../lib/supabase';
import { log, LogLevel } from '../utils/logger';
import { retryService } from './retryService';
import { networkService, NetworkQuality } from './networkService';
import { errorReportingService, ErrorSeverity } from './errorReportingService';
import { offlineQueueService, OperationType } from './offlineQueueService';
import { connectionState } from './connectionState';

// Constants for easy configuration
const AUTH_SESSION_TIMEOUT = 60000; // 60 seconds, less likely to timeout
const AUTH_CACHE_DURATION = 30000; // 30 seconds
const HEALTH_CHECK_INTERVAL = 60000; // 1 minute
const TOKEN_REFRESH_THRESHOLD = 300000; // 5 minutes before expiration
const CONNECTION_RECOVERY_INTERVAL = 30000; // 30 seconds


/**
 * Connection Service - Manages database connectivity and authentication session state.
 * Implements caching, request deduplication, and connection recovery to prevent
 * race conditions, timeouts, and handle unstable connections.
 */
export class ConnectionService {
  /**
   * Ensures both database connection and authenticated session are valid.
   * Returns true only if both are healthy.
   */
  public async ensureConnection(): Promise<boolean> {
    const dbOk = await this.checkDatabaseConnection();
    if (!dbOk) return false;
    const authOk = await this.ensureAuthSession();
    return dbOk && authOk;
  }
  private static instance: ConnectionService;

  // State for database health checks
  private healthCheckPromise: Promise<boolean> | null = null;
  private lastHealthCheckTimestamp: number = 0;

  // State for authentication session checks
  private authCheckPromise: Promise<boolean> | null = null;
  private lastAuthCheckTimestamp: number = 0;
  private authCheckResult: boolean | null = null;
  
  // Connection status tracking
  private isConnected: boolean = false;
  private connectionListeners: Set<(connected: boolean) => void> = new Set();
  private refreshTokenTimeout: number | null = null;
  private refreshInProgress: boolean = false;
  private refreshQueue: Array<() => void> = [];
  private recoveryInterval: number | null = null;
  private recoveryAttempts: number = 0;
  private maxRecoveryAttempts: number = 10; // Maximum number of recovery attempts before giving up

  private constructor() {
    log(LogLevel.INFO, '[ConnectionService] Initializing...');
    
    // Set up network status listener
    networkService.addStatusListener(this.handleNetworkStatusChange.bind(this));
    
    // Set up Supabase auth state change listener
    supabase.auth.onAuthStateChange((event, session) => {
      log(LogLevel.INFO, `[ConnectionService] Auth state change: ${event}`);
      
      if (event === 'SIGNED_IN') {
        this.setupTokenRefresh(session);
        this.clearAuthCache();
        this.checkDatabaseConnection();
      } else if (event === 'SIGNED_OUT') {
        this.clearTokenRefresh();
        this.clearAuthCache();
        this.stopConnectionRecovery();
      } else if (event === 'TOKEN_REFRESHED') {
        this.setupTokenRefresh(session);
        this.clearAuthCache();
      }
    });
    
    // Initialize connection status
    this.checkInitialConnection();
  }

  public static getInstance(): ConnectionService {
    if (!ConnectionService.instance) {
      ConnectionService.instance = new ConnectionService();
    }
    return ConnectionService.instance;
  }

  // --- Cache Management ---

  /**
   * Clears the cached authentication status.
   * Should be called on sign-in or sign-out events.
   */
  public clearAuthCache(): void {
    this.authCheckPromise = null;
    this.lastAuthCheckTimestamp = 0;
    this.authCheckResult = null;
    log(LogLevel.INFO, '🔄 [ConnectionService] Auth cache cleared.');
  }

  /**
   * Clears all caches in this service.
   */
  public clearAllCache(): void {
    this.healthCheckPromise = null;
    this.lastHealthCheckTimestamp = 0;
    this.clearAuthCache();
    log(LogLevel.INFO, '🔄 [ConnectionService] All service caches cleared.');
  }

  // --- Database Connection Check ---

  /**
   * Adds a listener for connection status changes
   * @param listener Function to call when connection status changes
   */
  public addConnectionListener(listener: (connected: boolean) => void): void {
    this.connectionListeners.add(listener);
    // Immediately notify with current status
    listener(this.isConnected);
  }

  /**
   * Removes a previously added connection listener
   * @param listener The listener function to remove
   */
  public removeConnectionListener(listener: (connected: boolean) => void): void {
    this.connectionListeners.delete(listener);
  }

  /**
   * Gets the current connection status
   */
  public isConnectionActive(): boolean {
    return this.isConnected;
  }

  /**
   * Checks the database connection health with caching and request deduplication.
   * Uses network quality information to adjust retry behavior.
   */
  public async checkDatabaseConnection(): Promise<boolean> {
    const now = Date.now();
    if (this.lastHealthCheckTimestamp && (now - this.lastHealthCheckTimestamp) < HEALTH_CHECK_INTERVAL) {
      log(LogLevel.INFO, '🔍 [ConnectionService] Returning cached database connection status (true).');
      connectionState.setStatus('connected');
      return true;
    }

    if (this.healthCheckPromise) {
      log(LogLevel.INFO, '🔍 [ConnectionService] Database connection check in progress, returning existing promise.');
      return this.healthCheckPromise;
    }

    log(LogLevel.INFO, '🔍 [ConnectionService] Performing new database connection check.');
    connectionState.setStatus('connecting');
    this.healthCheckPromise = new Promise(async (resolve) => {
      const start = Date.now();
      let aborted = false;
      let timeoutId: ReturnType<typeof setTimeout> | null = null;
      try {
        // Use a lightweight query with timeout based on network quality
        const networkStatus = networkService.getNetworkStatus();
        const timeout = this.getTimeoutForNetworkQuality(networkStatus.quality);

        const controller = new AbortController();
        timeoutId = setTimeout(() => {
          aborted = true;
          controller.abort();
          log(LogLevel.ERROR, `[ConnectionService] Database connection check aborted after ${timeout}ms.`);
        }, timeout);

        log(LogLevel.INFO, `[ConnectionService] Database connection check started (timeout: ${timeout}ms)`);
        // Use health check function instead of querying profiles table to avoid RLS issues
        const { error } = await supabase.rpc('health_check');

        const elapsed = Date.now() - start;
        if (timeoutId) clearTimeout(timeoutId);

        if (error) throw error;

        // Update connection status if it changed
        this.updateConnectionStatus(true);
        connectionState.setStatus('connected');
        log(LogLevel.SUCCESS, `✅ [ConnectionService] Database connection healthy after ${elapsed}ms.`);
        this.lastHealthCheckTimestamp = Date.now();
        resolve(true);
      } catch (error) {
        const elapsed = Date.now() - start;
        if (timeoutId) clearTimeout(timeoutId);
        // Update connection status if it changed
        this.updateConnectionStatus(false);

        // Report the error
        errorReportingService.reportError(
          error instanceof Error ? error : new Error('Database connection failed'),
          { source: 'checkDatabaseConnection', elapsed, aborted },
          ErrorSeverity.MEDIUM,
          ['connection', 'database']
        );

        if (aborted) {
          log(LogLevel.ERROR, `❌ [ConnectionService] Database connection check timed out after ${elapsed}ms.`);
          connectionState.setStatus('disconnected', 'Database connection timed out');
        } else {
          log(LogLevel.ERROR, `❌ [ConnectionService] Database connection failed after ${elapsed}ms:`, error);
          const errMsg = (typeof error === 'object' && error && 'message' in error) ? (error as any).message : String(error);
          connectionState.setStatus('error', errMsg || 'Database connection failed');
        }
        resolve(false);
      } finally {
        // Allow subsequent checks after this one completes
        this.healthCheckPromise = null;
      }
    });

    return this.healthCheckPromise;
  }

  // --- Authentication Session Check ---

  /**
   * Ensures the user has a valid, authenticated session.
   * Uses caching and request deduplication to prevent excessive checks.
   */
  public async ensureAuthSession(): Promise<boolean> {
    const now = Date.now();

    // 1. Return a fresh cached result if available
    if (this.authCheckResult !== null && (now - this.lastAuthCheckTimestamp) < AUTH_CACHE_DURATION) {
      log(LogLevel.INFO, `🔍 [ConnectionService] Returning cached auth result: ${this.authCheckResult}`);
      return this.authCheckResult;
    }

    // 2. Return an in-flight promise to deduplicate concurrent requests
    if (this.authCheckPromise) {
      log(LogLevel.INFO, '🔍 [ConnectionService] Auth check in progress, returning existing promise.');
      return this.authCheckPromise;
    }

    // 3. Perform a new check
    log(LogLevel.INFO, '🔍 [ConnectionService] Performing new auth session check.');
    this.authCheckPromise = this.performAuthSessionCheck();
    
    // After the promise completes, clear it so new checks can be made in the future
    this.authCheckPromise.finally(() => {
      this.authCheckPromise = null;
    });

    return this.authCheckPromise;
  }

  /**
   * Updates the connection status and notifies listeners if changed
   * @param connected The new connection status
   */
  private updateConnectionStatus(connected: boolean): void {
    if (this.isConnected !== connected) {
      log(LogLevel.INFO, `[ConnectionService] Connection status changed: ${connected ? 'connected' : 'disconnected'}`);
      this.isConnected = connected;
      connectionState.setStatus(connected ? 'connected' : 'disconnected');
      // Notify listeners
      for (const listener of this.connectionListeners) {
        try {
          listener(connected);
        } catch (error) {
          log(LogLevel.ERROR, '[ConnectionService] Error in connection listener:', error);
        }
      }
      // Handle connection state changes
      if (connected) {
        // If we're now connected, stop recovery attempts and process offline queue
        this.stopConnectionRecovery();
        this.processOfflineQueue();
      } else {
        // If we're now disconnected, start recovery attempts
        this.startConnectionRecovery();
      }
    }
  }
  
  /**
   * Starts the connection recovery process
   */
  private startConnectionRecovery(): void {
    if (this.recoveryInterval !== null) {
      // Already attempting recovery
      return;
    }
    
    this.recoveryAttempts = 0;
    log(LogLevel.INFO, '[ConnectionService] Starting connection recovery process');
    
    this.recoveryInterval = window.setInterval(() => {
      this.attemptConnectionRecovery();
    }, CONNECTION_RECOVERY_INTERVAL);
  }
  
  /**
   * Stops the connection recovery process
   */
  private stopConnectionRecovery(): void {
    if (this.recoveryInterval !== null) {
      window.clearInterval(this.recoveryInterval);
      this.recoveryInterval = null;
      this.recoveryAttempts = 0;
      log(LogLevel.INFO, '[ConnectionService] Stopped connection recovery process');
      
      // Dispatch recovery success event if we're connected
      if (this.isConnected) {
        this.dispatchRecoverySuccessEvent();
      }
    }
  }
  
  /**
   * Dispatches a custom event for connection recovery attempts
   */
  private dispatchRecoveryAttemptEvent(): void {
    try {
      const event = new CustomEvent('connection_recovery_attempt', {
        detail: {
          attempt: this.recoveryAttempts,
          maxAttempts: this.maxRecoveryAttempts
        }
      });
      window.dispatchEvent(event);
    } catch (error) {
      log(LogLevel.ERROR, '[ConnectionService] Error dispatching recovery attempt event:', error);
    }
  }
  
  /**
   * Dispatches a custom event for connection recovery success
   */
  private dispatchRecoverySuccessEvent(): void {
    try {
      const event = new CustomEvent('connection_recovery_success');
      window.dispatchEvent(event);
    } catch (error) {
      log(LogLevel.ERROR, '[ConnectionService] Error dispatching recovery success event:', error);
    }
  }
  
  /**
   * Attempts to recover the connection
   */
  private async attemptConnectionRecovery(): Promise<void> {
    if (this.isConnected) {
      this.stopConnectionRecovery();
      return;
    }
    
    this.recoveryAttempts++;
    log(LogLevel.INFO, `[ConnectionService] Connection recovery attempt ${this.recoveryAttempts}/${this.maxRecoveryAttempts}`);
    
    // Dispatch recovery attempt event
    this.dispatchRecoveryAttemptEvent();
    
    // Check if we've exceeded the maximum number of attempts
    if (this.recoveryAttempts > this.maxRecoveryAttempts) {
      log(LogLevel.WARN, '[ConnectionService] Maximum recovery attempts reached, giving up');
      this.stopConnectionRecovery();
      
      // Report the error
      errorReportingService.reportError(
        new Error('Connection recovery failed after maximum attempts'),
        { source: 'attemptConnectionRecovery', attempts: this.recoveryAttempts },
        ErrorSeverity.MEDIUM,
        ['connection', 'recovery']
      );
      
      return;
    }
    
    // Check network status first
    const networkStatus = networkService.getNetworkStatus();
    if (!networkStatus.online) {
      log(LogLevel.INFO, '[ConnectionService] Network is offline, skipping recovery attempt');
      return;
    }
    
    try {
      // Try to check database connection and auth session
      const dbOk = await this.checkDatabaseConnection();
      if (!dbOk) {
        log(LogLevel.WARN, '[ConnectionService] Database connection check failed during recovery');
        return;
      }
      
      const authOk = await this.ensureAuthSession();
      if (!authOk) {
        log(LogLevel.WARN, '[ConnectionService] Auth session check failed during recovery');
        return;
      }
      
      // If both checks passed, we're connected
      log(LogLevel.SUCCESS, '[ConnectionService] Connection recovered successfully');
      this.updateConnectionStatus(true);
      this.stopConnectionRecovery();
      
      // Dispatch recovery success event
      this.dispatchRecoverySuccessEvent();
    } catch (error) {
      log(LogLevel.ERROR, '[ConnectionService] Error during connection recovery:', error);
    }
  }
  
  /**
   * Processes the offline queue when connection is restored
   */
  private async processOfflineQueue(): Promise<void> {
    try {
      log(LogLevel.INFO, '[ConnectionService] Processing offline queue after connection restored');
      await offlineQueueService.processQueue();
    } catch (error) {
      log(LogLevel.ERROR, '[ConnectionService] Error processing offline queue:', error);
      
      // Report the error
      errorReportingService.reportError(
        error instanceof Error ? error : new Error('Error processing offline queue'),
        { source: 'processOfflineQueue' },
        ErrorSeverity.MEDIUM,
        ['connection', 'offline_queue']
      );
    }
  }

  /**
   * Handles network status changes
   */
  /**
   * Performs an initial connection check when the service starts
   */
  private async checkInitialConnection(): Promise<void> {
    try {
      const networkStatus = networkService.getNetworkStatus();
      if (networkStatus.online) {
        await this.checkDatabaseConnection();
        await this.ensureAuthSession();
      } else {
        this.updateConnectionStatus(false);
      }
    } catch (error) {
      log(LogLevel.ERROR, '[ConnectionService] Initial connection check failed:', error);
      this.updateConnectionStatus(false);
    }
  }

  /**
   * Handles network status changes
   */
  private handleNetworkStatusChange(status: any): void {
    log(LogLevel.INFO, `[ConnectionService] Network status changed: ${status.quality}`);
    if (status.online && status.quality !== NetworkQuality.POOR) {
      connectionState.setStatus('connecting');
      // If we're back online with decent quality, check connection
      this.checkDatabaseConnection();
      this.ensureAuthSession();
      // Start connection recovery if we're not connected
      if (!this.isConnected) {
        this.startConnectionRecovery();
      }
    } else if (!status.online) {
      connectionState.setStatus('disconnected', 'Network offline');
      // If we're offline, update connection status
      this.updateConnectionStatus(false);
      this.stopConnectionRecovery();
    } else if (status.quality === NetworkQuality.POOR) {
      // If network quality is poor, we might want to adjust timeouts
      // but still try to maintain connection
      log(LogLevel.WARN, '[ConnectionService] Poor network quality detected, adjusting timeouts');
      connectionState.setStatus('connecting');
    }
  }

  /**
   * Gets an appropriate timeout value based on network quality
   */
  private getTimeoutForNetworkQuality(quality: NetworkQuality): number {
    switch (quality) {
      case NetworkQuality.POOR:
        return 15000; // 15 seconds
      case NetworkQuality.FAIR:
        return 10000; // 10 seconds
      case NetworkQuality.GOOD:
        return 5000;  // 5 seconds
      case NetworkQuality.EXCELLENT:
        return 3000;  // 3 seconds
      case NetworkQuality.OFFLINE:
        return 2000;  // 2 seconds (will fail quickly)
      default:
        return 10000; // 10 seconds default
    }
  }

  /**
   * The internal logic for checking and refreshing the auth session.
   * Implements token refresh scheduling and queue management.
   */
  private async performAuthSessionCheck(): Promise<boolean> {
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Auth session check timeout')), AUTH_SESSION_TIMEOUT)
    );

    const start = Date.now();
    try {
      log(LogLevel.INFO, '[ConnectionService] Calling supabase.auth.getSession()...');
      const sessionResult = await Promise.race([supabase.auth.getSession(), timeoutPromise]);
      const elapsed = Date.now() - start;
      log(LogLevel.INFO, `[ConnectionService] supabase.auth.getSession() result after ${elapsed}ms:`, sessionResult);
      const { data: { session }, error } = sessionResult;

      if (error) {
        log(LogLevel.ERROR, `❌ [ConnectionService] Error getting session after ${elapsed}ms:`, error);
        this.updateAuthCache(false);
        // Report the error
        errorReportingService.reportError(
          error instanceof Error ? error : new Error('Error getting auth session'),
          { source: 'performAuthSessionCheck', elapsed },
          ErrorSeverity.MEDIUM,
          ['connection', 'auth']
        );
        return false;
      }

      if (!session) {
        log(LogLevel.WARN, `⚠️ [ConnectionService] No active session found after ${elapsed}ms.`);
        this.updateAuthCache(false);
        return false;
      }

      // Set up token refresh scheduling
      this.setupTokenRefresh(session);

      // Refresh the session if it's expired or will expire soon
      const isExpiringSoon = session.expires_at && session.expires_at * 1000 < Date.now() + TOKEN_REFRESH_THRESHOLD;
      if (isExpiringSoon) {
        log(LogLevel.INFO, `🔄 [ConnectionService] Session expiring soon, attempting refresh after ${elapsed}ms...`);
        return await this.refreshAuthToken();
      }

      log(LogLevel.SUCCESS, `✅ [ConnectionService] Auth session is valid after ${elapsed}ms.`);
      this.updateAuthCache(true);
      return true;
    } catch (err) {
      const elapsed = Date.now() - start;
      log(LogLevel.ERROR, `❌ [ConnectionService] performAuthSessionCheck failed after ${elapsed}ms:`, err);
      this.updateAuthCache(false);
      return false;
    }
  }

  /**
   * Updates the authentication cache state.
   */
  private updateAuthCache(result: boolean): void {
    this.authCheckResult = result;
    this.lastAuthCheckTimestamp = Date.now();
  }

  /**
   * Sets up token refresh scheduling based on session expiration
   */
  private setupTokenRefresh(session: any): void {
    // Clear any existing refresh timeout
    this.clearTokenRefresh();
    
    if (!session || !session.expires_at) {
      return;
    }
    
    // Calculate when to refresh (5 minutes before expiration)
    const expiresAt = session.expires_at * 1000; // Convert to milliseconds
    const refreshAt = expiresAt - TOKEN_REFRESH_THRESHOLD;
    const now = Date.now();
    const timeUntilRefresh = Math.max(0, refreshAt - now);
    
    if (timeUntilRefresh <= 0) {
      // If already past the refresh threshold, refresh immediately
      this.refreshAuthToken();
      return;
    }
    
    // Schedule the refresh
    log(LogLevel.INFO, `[ConnectionService] Scheduling token refresh in ${Math.round(timeUntilRefresh / 1000)} seconds`);
    this.refreshTokenTimeout = window.setTimeout(() => {
      this.refreshAuthToken();
    }, timeUntilRefresh);
  }

  /**
   * Clears any scheduled token refresh
   */
  private clearTokenRefresh(): void {
    if (this.refreshTokenTimeout !== null) {
      window.clearTimeout(this.refreshTokenTimeout);
      this.refreshTokenTimeout = null;
    }
  }

  /**
   * Refreshes the authentication token with queue management to prevent multiple concurrent refreshes
   */
  private async refreshAuthToken(): Promise<boolean> {
    // If a refresh is already in progress, add to queue
    if (this.refreshInProgress) {
      log(LogLevel.INFO, '[ConnectionService] Token refresh already in progress, adding to queue');
      return new Promise<boolean>((resolve) => {
        this.refreshQueue.push(() => resolve(true));
      });
    }
    
    this.refreshInProgress = true;
    
    try {
      log(LogLevel.INFO, '[ConnectionService] Refreshing auth token...');
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        log(LogLevel.ERROR, '❌ [ConnectionService] Token refresh failed:', error);
        this.updateAuthCache(false);
        
        // Report the error
        errorReportingService.reportError(
          error,
          { source: 'refreshAuthToken' },
          ErrorSeverity.MEDIUM,
          ['connection', 'auth', 'token_refresh']
        );
        
        return false;
      }
      
      log(LogLevel.SUCCESS, '✅ [ConnectionService] Token refreshed successfully');
      this.updateAuthCache(true);
      
      // Set up the next refresh
      if (data.session) {
        this.setupTokenRefresh(data.session);
      }
      
      return true;
    } catch (error) {
      log(LogLevel.ERROR, '❌ [ConnectionService] Error during token refresh:', error);
      this.updateAuthCache(false);
      return false;
    } finally {
      this.refreshInProgress = false;
      
      // Process any queued refreshes
      if (this.refreshQueue.length > 0) {
        log(LogLevel.INFO, `[ConnectionService] Processing ${this.refreshQueue.length} queued token refresh callbacks`);
        const callbacks = [...this.refreshQueue];
        this.refreshQueue = [];
        callbacks.forEach(callback => callback());
      }
    }
  }

  // --- Execution Wrapper ---

  /**
   * A wrapper to execute a database operation with enhanced retry logic and connection handling.
   * Uses the RetryService for exponential backoff and jitter.
   * 
   * If the operation fails due to network issues, it can optionally be queued for later execution.
   */
  public async executeWithRetry<T>(
    operation: () => Promise<T>, 
    operationName: string, 
    options: {
      queueOffline?: boolean;
      entityType?: string;
      entityId?: string;
      operationType?: string;
      priority?: number;
      customData?: any;
    } = {}
  ): Promise<T | null> {
    // Check network status first
    const networkStatus = networkService.getNetworkStatus();
    if (!networkStatus.online) {
      log(LogLevel.WARN, `⚠️ [ConnectionService] Cannot execute ${operationName} - network is offline`);
      // If queueOffline is true, add to offline queue
      if (options.queueOffline && options.entityType) {
        log(LogLevel.INFO, `[ConnectionService] Queueing ${operationName} for offline execution`);
        offlineQueueService.enqueue({
          entityType: options.entityType,
          entityId: options.entityId || 'unknown',
          type: (options.operationType as OperationType) || OperationType.CUSTOM,
          data: {
            operationName,
            ...options.customData
          }
        });
      }
      return null;
    }

    // If network quality is poor, consider queueing as well
    if (networkStatus.quality === NetworkQuality.POOR && options.queueOffline && options.entityType) {
      log(LogLevel.WARN, `⚠️ [ConnectionService] Network quality is poor, queueing ${operationName}`);
      offlineQueueService.enqueue({
        entityType: options.entityType,
        entityId: options.entityId || 'unknown',
        type: (options.operationType as OperationType) || OperationType.CUSTOM,
        data: {
          operationName,
          ...options.customData
        }
      });
      return null;
    }

    // --- Reconnection logic ---
    // If not connected, attempt to reconnect before DB operation
    const { connectionState } = await import('./connectionState');
    let attempts = 0;
    const maxAttempts = 3;
    while (connectionState.getStatus() !== 'connected' && attempts < maxAttempts) {
      log(LogLevel.WARN, `[ConnectionService] Not connected (status: ${connectionState.getStatus()}), attempting to reconnect (${attempts + 1}/${maxAttempts})`);
      await this.checkDatabaseConnection();
      attempts++;
      if (connectionState.getStatus() === 'connected') break;
      // Wait a bit before retrying
      await new Promise(res => setTimeout(res, 1000 * attempts));
    }
    if (connectionState.getStatus() !== 'connected') {
      log(LogLevel.ERROR, `[ConnectionService] Failed to reconnect after ${maxAttempts} attempts. Aborting ${operationName}.`);
      if (options.queueOffline && options.entityType) {
        offlineQueueService.enqueue({
          entityType: options.entityType,
          entityId: options.entityId || 'unknown',
          type: (options.operationType as OperationType) || OperationType.CUSTOM,
          data: {
            operationName,
            ...options.customData
          }
        });
      }
      return null;
    }

    // Determine retry options based on network quality
    const retryOptions = this.getRetryOptionsForNetworkQuality(networkStatus.quality);

    // Use the retry service for execution with exponential backoff
    const result = await retryService.executeWithRetry(
      async () => {
        // Check connection before executing
        const connectionReady = await this.checkDatabaseConnection();
        if (!connectionReady) {
          log(LogLevel.ERROR, `❌ [ConnectionService] Cannot execute ${operationName} - database connection not available`);
          throw new Error('Database connection not available');
        }

        // Check auth session if needed
        const authOk = await this.ensureAuthSession();
        if (!authOk) {
          log(LogLevel.ERROR, `❌ [ConnectionService] Cannot execute ${operationName} - auth session not available`);
          throw new Error('Auth session not available');
        }

        // The operation itself should check for auth, but this provides a good entry point
        log(LogLevel.INFO, `🚀 [ConnectionService] Executing ${operationName}`);
        return await operation();
      },
      operationName,
      retryOptions
    );

    // If the operation failed and queueOffline is true, add to offline queue
    if (result === null && options.queueOffline && options.entityType) {
      log(LogLevel.INFO, `[ConnectionService] Operation ${operationName} failed, queueing for later`);
      offlineQueueService.enqueue({
        entityType: options.entityType,
        entityId: options.entityId || 'unknown',
        type: (options.operationType as OperationType) || OperationType.CUSTOM,
        data: {
          operationName,
          ...options.customData
        }
      });
    }

    return result;
  }
  
  /**
   * Gets appropriate retry options based on network quality
   */
  private getRetryOptionsForNetworkQuality(quality: NetworkQuality): any {
    switch (quality) {
      case NetworkQuality.POOR:
        return {
          maxRetries: 5,
          initialDelayMs: 2000,
          maxDelayMs: 60000
        };
      case NetworkQuality.FAIR:
        return {
          maxRetries: 4,
          initialDelayMs: 1000,
          maxDelayMs: 30000
        };
      case NetworkQuality.GOOD:
      case NetworkQuality.EXCELLENT:
        return {
          maxRetries: 3,
          initialDelayMs: 500,
          maxDelayMs: 10000
        };
      default:
        return {
          maxRetries: 3,
          initialDelayMs: 1000,
          maxDelayMs: 30000
        };
    }
  }
}

// Export a singleton instance for use across the application
export const connectionService = ConnectionService.getInstance();
