import React from "react";
import { MessageBubbleProps } from "./MessageBubbleProps";

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isUser,
  isAI,
  isLoading,
  isError,
  timestamp,
}) => {
  return (
    <div
      className={`message-bubble ${isUser ? "user" : "ai"} ${isLoading ? "loading" : ""} ${isError ? "error" : ""}`}
      data-oid="9y-s.l2"
    >
      <p data-oid="j_jm81k">{message}</p>
    </div>
  );
};

export default MessageBubble;
