// Seed script for 6 Human Needs Assessment
// This script creates the assessment data based on the 6HN-questionnaire.md file

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables from .env.local file
require('dotenv').config({ path: path.join(__dirname, 'frontend', '.env.local') });

// You'll need to set these environment variables or replace with your actual values
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://your-project-id.supabase.co';
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_KEY || 'your-service-key-here';

console.log('🔍 Environment check:');
console.log('- Supabase URL:', supabaseUrl);
console.log('- Service Key available:', supabaseServiceKey ? 'Yes' : 'No');
console.log('- Service Key length:', supabaseServiceKey ? supabaseServiceKey.length : 0);

// Validate environment variables
if (!supabaseUrl || supabaseUrl === 'https://your-project-id.supabase.co') {
  console.error('❌ VITE_SUPABASE_URL is not set or is using the default value');
  process.exit(1);
}

if (!supabaseServiceKey || supabaseServiceKey === 'your-service-key-here') {
  console.error('❌ VITE_SUPABASE_SERVICE_KEY is not set or is using the default value');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const assessmentData = {
  title: '6 Human Needs Assessment',
  description: 'Discover your core human needs and how they shape your life decisions and behaviors.',
  icon: '/assets/6hn-icon.png',
  type: '6HN',
  is_active: true
};

const questions = [
  // Certainty Questions
  {
    category: 'Certainty',
    question_text: 'What makes you feel most secure or in control?',
    follow_up_text: 'How do you typically respond when things feel uncertain or out of control?',
    order: 1,
    quick_answers: [
      { answer_text: 'Routines give me stability.', score_value: 3, order: 1 },
      { answer_text: 'I value flexibility over structure.', score_value: 1, order: 2 },
      { answer_text: 'I\'m unsure—help me figure it out!', score_value: 2, order: 3 }
    ]
  },
  {
    category: 'Certainty',
    question_text: 'How do you typically respond when things feel uncertain or out of control?',
    follow_up_text: 'Are there areas of your life where too much stability might be holding you back?',
    order: 7,
    quick_answers: [
      { answer_text: 'I try to regain control immediately.', score_value: 3, order: 1 },
      { answer_text: 'I adapt and go with the flow.', score_value: 1, order: 2 },
      { answer_text: 'I get anxious and need support.', score_value: 2, order: 3 }
    ]
  },
  {
    category: 'Certainty',
    question_text: 'Can you describe a situation where you felt completely safe and at peace?',
    follow_up_text: 'If you could create a "safety net" for yourself, what would it include?',
    order: 13,
    quick_answers: [
      { answer_text: 'When I\'m at home with loved ones.', score_value: 3, order: 1 },
      { answer_text: 'During a quiet moment alone.', score_value: 2, order: 2 },
      { answer_text: 'In a place where I feel accomplished.', score_value: 2, order: 3 }
    ]
  },
  {
    category: 'Certainty',
    question_text: 'What routines or habits help you maintain a sense of stability in your daily life?',
    follow_up_text: 'What\'s one step you could take today to build more consistency into your routine?',
    order: 19,
    quick_answers: [
      { answer_text: 'Morning meditation keeps me grounded.', score_value: 3, order: 1 },
      { answer_text: 'Following a strict schedule helps me stay focused.', score_value: 3, order: 2 },
      { answer_text: 'I don\'t have routines—I prefer spontaneity.', score_value: 1, order: 3 }
    ]
  },

  // Uncertainty/Variety Questions
  {
    category: 'Uncertainty/Variety',
    question_text: 'When was the last time you felt truly excited or challenged? What happened?',
    follow_up_text: 'What kinds of activities or experiences make you feel alive and energized?',
    order: 2,
    quick_answers: [
      { answer_text: 'A recent trip pushed me out of my comfort zone.', score_value: 3, order: 1 },
      { answer_text: 'Starting a new project energized me.', score_value: 3, order: 2 },
      { answer_text: 'I haven\'t felt excited in a while.', score_value: 1, order: 3 }
    ]
  },
  {
    category: 'Uncertainty/Variety',
    question_text: 'What kinds of activities or experiences make you feel alive and energized?',
    follow_up_text: 'How can you introduce small changes to break monotony without overwhelming yourself?',
    order: 8,
    quick_answers: [
      { answer_text: 'Traveling to new places.', score_value: 3, order: 1 },
      { answer_text: 'Learning something completely new.', score_value: 3, order: 2 },
      { answer_text: 'Trying adventurous activities like hiking or skydiving.', score_value: 3, order: 3 }
    ]
  },
  {
    category: 'Uncertainty/Variety',
    question_text: 'Do you ever feel stuck in routines or responsibilities that lack excitement? Why?',
    follow_up_text: 'Have you ever taken a big risk that paid off? What did you learn from that experience?',
    order: 14,
    quick_answers: [
      { answer_text: 'Yes, because I prioritize stability over novelty.', score_value: 1, order: 1 },
      { answer_text: 'No, I find excitement in small changes.', score_value: 2, order: 2 },
      { answer_text: 'Sometimes, but I don\'t know how to break free.', score_value: 2, order: 3 }
    ]
  },
  {
    category: 'Uncertainty/Variety',
    question_text: 'If you could plan an adventure tomorrow, what would it look like?',
    follow_up_text: 'How do you manage the tension between wanting stability and craving change?',
    order: 20,
    quick_answers: [
      { answer_text: 'Exploring a foreign city.', score_value: 3, order: 1 },
      { answer_text: 'Spending a day in nature.', score_value: 2, order: 2 },
      { answer_text: 'Attending a cultural event or festival.', score_value: 3, order: 3 }
    ]
  },

  // Significance Questions
  {
    category: 'Significance',
    question_text: 'How do you ensure that your efforts are recognized or valued by others?',
    follow_up_text: 'How do you define success for yourself, independent of external validation?',
    order: 3,
    quick_answers: [
      { answer_text: 'I communicate my contributions clearly.', score_value: 3, order: 1 },
      { answer_text: 'I don\'t seek recognition—it\'s not important to me.', score_value: 1, order: 2 },
      { answer_text: 'I wish I knew how to be more acknowledged.', score_value: 2, order: 3 }
    ]
  },
  {
    category: 'Significance',
    question_text: 'What makes you feel proud of who you are and what you\'ve accomplished?',
    follow_up_text: 'What qualities do you admire most about yourself, and how can you celebrate them more often?',
    order: 9,
    quick_answers: [
      { answer_text: 'Overcoming personal challenges.', score_value: 2, order: 1 },
      { answer_text: 'Achieving professional milestones.', score_value: 3, order: 2 },
      { answer_text: 'Being there for others during tough times.', score_value: 2, order: 3 }
    ]
  },
  {
    category: 'Significance',
    question_text: 'Can you share a moment when you felt truly appreciated or acknowledged?',
    follow_up_text: 'How do you handle situations where you feel unappreciated?',
    order: 15,
    quick_answers: [
      { answer_text: 'When someone thanked me for my help.', score_value: 2, order: 1 },
      { answer_text: 'After receiving public recognition.', score_value: 3, order: 2 },
      { answer_text: 'I can\'t recall such a moment.', score_value: 1, order: 3 }
    ]
  },
  {
    category: 'Significance',
    question_text: 'In what ways do you stand out from others? How does that make you feel?',
    follow_up_text: 'What\'s one way you could contribute more value to your community or workplace?',
    order: 21,
    quick_answers: [
      { answer_text: 'My creativity sets me apart.', score_value: 3, order: 1 },
      { answer_text: 'My resilience inspires those around me.', score_value: 3, order: 2 },
      { answer_text: 'I don\'t think I stand out much.', score_value: 1, order: 3 }
    ]
  }
];

async function seedAssessment() {
  try {
    console.log('🌱 Starting 6HN Assessment seeding...');

    // Insert the assessment
    const { data: assessment, error: assessmentError } = await supabase
      .from('assessments')
      .insert(assessmentData)
      .select()
      .single();

    if (assessmentError) {
      throw assessmentError;
    }

    console.log('✅ Assessment created:', assessment.title);

    // Insert questions and their quick answers
    for (const questionData of questions) {
      const { quick_answers, ...questionInfo } = questionData;
      
      // Insert question
      const { data: question, error: questionError } = await supabase
        .from('assessment_questions')
        .insert({
          ...questionInfo,
          assessment_id: assessment.id
        })
        .select()
        .single();

      if (questionError) {
        throw questionError;
      }

      // Insert quick answers for this question
      const answersToInsert = quick_answers.map(answer => ({
        ...answer,
        question_id: question.id
      }));

      const { error: answersError } = await supabase
        .from('assessment_quick_answers')
        .insert(answersToInsert);

      if (answersError) {
        throw answersError;
      }

      console.log(`✅ Question ${question.order} created with ${quick_answers.length} answers`);
    }

    console.log('🎉 6HN Assessment seeding completed successfully!');
    console.log(`📊 Created 1 assessment with ${questions.length} questions`);

  } catch (error) {
    console.error('❌ Error seeding assessment:', error);
    process.exit(1);
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedAssessment();
}

module.exports = { seedAssessment };
