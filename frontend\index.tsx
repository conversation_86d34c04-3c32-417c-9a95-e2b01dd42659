// --- Debug: Send console output to backend for real-time logging ---
// Debug log server integration flag (default: off)
const ENABLE_DEBUG_LOG_SERVER = false;
const origLog = console.log;
const origError = console.error;
function sendLogToServer(type: string, ...args: any[]) {
  if (!ENABLE_DEBUG_LOG_SERVER) return;
  fetch("http://localhost:3001/api/log", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      type,
      message: args.map(String).join(" "),
      timestamp: new Date().toISOString(),
    }),
  });
}
console.log = function (...args: any[]) {
  origLog.apply(console, args);
  if (ENABLE_DEBUG_LOG_SERVER) sendLogToServer("log", ...args);
};
console.error = function (...args: any[]) {
  origError.apply(console, args);
  if (ENABLE_DEBUG_LOG_SERVER) sendLogToServer("error", ...args);
};
// Persist Supabase recovery token from hash to sessionStorage before React renders
(function persistSupabaseRecoveryToken() {
  if (typeof window !== "undefined" && window.location.hash) {
    const hash = window.location.hash.substring(1); // remove '#'
    const params = new URLSearchParams(hash);
    const accessToken = params.get("access_token");
    const refreshToken = params.get("refresh_token");
    const type = params.get("type");
    if (accessToken && type === "recovery") {
      sessionStorage.setItem("supabase_recovery_access_token", accessToken);
      sessionStorage.setItem("supabase_recovery_type", type);
      if (refreshToken) {
        sessionStorage.setItem("supabase_recovery_refresh_token", refreshToken);
      }
    }
  }
})();

import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import { I18nextProvider } from "react-i18next";
import i18n, { initializeI18n } from "./i18n"; // Import the i18n instance and the new initializer
import { BrowserRouter } from "react-router-dom";

const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);

// Initialize i18next and then render the app
initializeI18n()
  .then((initializedI18nInstance) => {
    root.render(
      <React.StrictMode data-oid="9ur2fqk">
        <I18nextProvider i18n={initializedI18nInstance} data-oid="x2vf2xw">
          <BrowserRouter data-oid="1o3ftd5">
            <App data-oid="4ncpz4g" />
          </BrowserRouter>
        </I18nextProvider>
      </React.StrictMode>,
    );
  })
  .catch((error) => {
    // Optionally render a user-friendly error message to the DOM
    root.render(
      <div
        style={{
          padding: "20px",
          textAlign: "center",
          fontFamily: "sans-serif",
          color: "#333",
        }}
        data-oid="e_7j8kn"
      >
        <h1 data-oid="j3og7:x">Application Error</h1>
        <p data-oid="gxw2_.q">
          There was an issue loading essential application resources
          (translations).
        </p>
        <p data-oid="m_8v87z">
          Please try refreshing the page. If the problem persists, contact
          support.
        </p>
      </div>,
    );
  });
