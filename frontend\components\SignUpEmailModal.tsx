import React from "react";
import { useTranslation } from "react-i18next";
import { CloseIcon } from "../constants";

interface SignUpEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSignIn: () => void;
  onGoogleSignUp: () => void;
  onGithubSignUp: () => void;
  onSignUp: (email: string, password: string) => void;
  error?: string | null;
}

const SignUpEmailModal: React.FC<SignUpEmailModalProps> = ({
  isOpen,
  onClose,
  onSignIn,
  onGoogleSignUp,
  onGithubSignUp,
  onSignUp,
  error,
}) => {
  const { t } = useTranslation();
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  if (!isOpen) return null;
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
      role="dialog"
      aria-modal="true"
    >
      <div className="w-full sm:max-w-md min-h-screen flex flex-col text-center relative">
        <div className="my-auto w-full">
          <div className="dark:text-gray-100 rounded-2xl bg-white dark:bg-slate-800 border-b-2 border-black/10 p-10 relative">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 p-1.5 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full"
              aria-label="Close"
            >
              <CloseIcon className="w-5 h-5" />
            </button>
            <form
              className="flex flex-col justify-center"
              onSubmit={(e) => {
                e.preventDefault();
                onSignUp(email, password);
              }}
            >
              <div className="self-center mb-6 size-14">
                <div className="h-full w-full aspect-square rounded-2xl bg-gradient-to-br from-[#1A1919] to-[#64646B] flex justify-center items-center">
                  {/* No logo, just reserved space */}
                </div>
              </div>
              {/* Title and subtitle */}
              <div className="mb-4">
                <div className="text-2xl font-bold textGradient bg-gradient-to-r from-[#191a1d] via-[#747689] to-[#191a1d] bg-clip-text text-transparent">
                  {t("createYourAccount")}
                </div>
                <div className="text-sm text-gray-500 font-normal mb-4 mt-2">
                  {t("signUpToUnlockFeatures")}
                </div>
              </div>

              <div className="loginForm flex flex-col">
                {/* Email field with label and icon */}
                <div className="mb-2">
                  <div className="text-sm font-medium text-left mb-1">
                    {t("email")}
                  </div>
                  <div className="flex gap-2 items-center px-3 py-2.5 rounded-lg border border-black/10 dark:border-white/10">
                    {/* Email SVG icon */}
                    <svg
                      className="size-5 opacity-80"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      viewBox="0 0 20 20"
                    >
                      <g opacity="0.8">
                        <path
                          d="M1.66664 14.25C1.66664 15.3546 2.56207 16.25 3.66664 16.25H16.3333C17.4379 16.25 18.3333 15.3546 18.3333 14.25V10V5.75C18.3333 4.64543 17.4379 3.75 16.3333 3.75H9.99997H3.66664C2.56207 3.75 1.66664 4.64543 1.66664 5.75V10V14.25Z"
                          strokeLinejoin="round"
                        ></path>
                        <path
                          d="M3.00002 4.5L9.99998 10L17 4.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        ></path>
                      </g>
                    </svg>
                    <input
                      type="email"
                      className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500"
                      autoComplete="email"
                      name="email"
                      placeholder={t("enterYourEmail")}
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>
                </div>
                {/* Password field with label and icon */}
                <div className="text-sm font-medium text-left mb-1">
                  {t("password")}
                </div>
                <div className="flex gap-2 items-center px-3 py-2.5 mb-2 rounded-lg border border-black/10 dark:border-white/10">
                  {/* Password SVG icon */}
                  <svg
                    className="size-5 opacity-80"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    viewBox="0 0 20 20"
                  >
                    <g opacity="0.8">
                      <path
                        d="M17.5 16.3334V11.1667C17.5 10.0621 16.6064 9.16669 15.5018 9.16669H4.49558C3.39101 9.16669 2.5 10.0621 2.5 11.1667V16.3334C2.5 17.4379 3.39543 18.3334 4.5 18.3334H15.5C16.6046 18.3334 17.5 17.4379 17.5 16.3334Z"
                        strokeLinejoin="round"
                      ></path>
                      <path
                        d="M5.83336 9.16669V5.83335C5.83336 3.53217 7.69886 1.66669 10 1.66669C12.3012 1.66669 14.1667 3.53217 14.1667 5.83335V9.16669"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      ></path>
                      <path
                        d="M10 12.5V15"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      ></path>
                    </g>
                  </svg>
                  <input
                    type="password"
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500"
                    placeholder={t("enterYourPassword")}
                    autoComplete="new-password"
                    name="password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </div>
              </div>
              {error && (
                <div className="text-red-500 text-xs mb-2">{error}</div>
              )}
              <button
                className="ButtonSignIn ButtonCreateAccount button-gradient bg-[#222428] dark:bg-slate-700 hover:bg-opacity-90 dark:hover:bg-slate-600 text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mt-3"
                type="submit"
              >
                {t("createAccount")}
              </button>
              <button
                type="button"
                className="ButtonSkipForNow flex justify-center items-center mt-3 dark:text-gray-300 dark:hover:text-white w-full rounded-lg border border-black/10 dark:border-white/10 font-medium text-sm py-2.5 dark:hover:bg-white/5"
                onClick={onClose}
              >
                {t("skipForNow")}
              </button>
              <div className="flex items-center justify-center gap-2 mt-3 text-sm">
                <div className="flex-1 text-sm text-center font-normal">
                  {t("alreadyHaveAccount")}{" "}
                  <button
                    className="font-medium underline"
                    type="button"
                    onClick={onSignIn}
                  >
                    {t("signIn")}
                  </button>
                </div>
                <div
                  className="h-full min-h-4 w-[1px] bg-black/20 dark:bg-gray-700"
                  role="separator"
                  aria-orientation="vertical"
                />

                <div className="flex flex-1 justify-center items-center gap-3">
                  <div className="flex flex-1 justify-center items-center gap-3">
                    {t("quickLogin")}
                  </div>
                  <button
                    className="ButtonContinueWithGoogle bg-white border border-gray-200 dark:bg-transparent dark:border-gray-700 rounded-lg p-2 hover:bg-gray-50 dark:hover:bg-white/5 transition-colors"
                    type="button"
                    onClick={onGoogleSignUp}
                  >
                    {/* Google SVG icon */}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 48 48"
                      className="size-5"
                    >
                      <path
                        fill="#EA4335"
                        d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
                      />

                      <path
                        fill="#4285F4"
                        d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
                      />

                      <path
                        fill="#FBBC05"
                        d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
                      />

                      <path
                        fill="#34A853"
                        d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
                      />

                      <path fill="none" d="M0 0h48v48H0z" />
                    </svg>
                  </button>
                  <button
                    className="ButtonContinueWithGithub bg-white border border-gray-200 dark:bg-transparent dark:border-gray-700 rounded-lg p-2 hover:bg-gray-50 dark:hover:bg-white/5 transition-colors"
                    type="button"
                    onClick={onGithubSignUp}
                  >
                    {/* Github SVG icon */}
                    <svg
                      className="size-5"
                      width={21}
                      height={20}
                      viewBox="0 0 21 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.3972 0C4.73243 0 0.144226 4.5882 0.144226 10.253C0.144226 14.7899 3.07914 18.6219 7.15469 19.9804C7.66734 20.0702 7.85958 19.7626 7.85958 19.4934C7.85958 19.2499 7.84676 18.4425 7.84676 17.5838C5.27071 18.058 4.60426 16.9558 4.3992 16.3791C4.28386 16.0843 3.78403 15.1744 3.34828 14.9308C2.98942 14.7386 2.47677 14.2644 3.33546 14.2516C4.14288 14.2388 4.71961 14.995 4.91185 15.3025C5.83462 16.8533 7.30848 16.4175 7.89803 16.1484C7.98774 15.482 8.25688 15.0334 8.55165 14.777C6.27037 14.5207 3.88656 13.6364 3.88656 9.71468C3.88656 8.59962 4.28386 7.67691 4.93749 6.9592C4.83496 6.70287 4.4761 5.65195 5.04002 4.24216C5.04002 4.24216 5.8987 3.97302 7.85958 5.29309C8.67982 5.0624 9.55132 4.94705 10.4228 4.94705C11.2943 4.94705 12.1658 5.0624 12.9861 5.29309C14.947 3.96021 15.8056 4.24216 15.8056 4.24216C16.3695 5.65195 16.0107 6.70287 15.9082 6.9592C16.5618 7.67691 16.9591 8.58685 16.9591 9.71468C16.9591 13.6492 14.5625 14.5207 12.2812 14.777C12.6528 15.0975 12.9733 15.7127 12.9733 16.6738C12.9733 18.0452 12.9604 19.1474 12.9604 19.4934C12.9604 19.7626 13.1527 20.0829 13.6654 19.9804C17.7153 18.6219 20.6501 14.777 20.6501 10.253C20.6501 4.5882 16.0619 0 10.3972 0Z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUpEmailModal;
