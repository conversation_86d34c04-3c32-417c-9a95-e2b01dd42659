<!DOCTYPE html>
<html>
<head>
    <title>Simple Streaming Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .output { background: #f5f5f5; padding: 10px; margin: 10px 0; min-height: 100px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Simple Streaming Test</h1>
    
    <button onclick="testGeminiAPI()">Test Gemini API</button>
    <button onclick="testOpenRouterAPI()">Test OpenRouter API</button>
    
    <div class="output" id="output">Click a button to test API streaming...</div>
    
    <script>
        window.testGeminiAPI = async () => {
            const output = document.getElementById('output');
            output.innerHTML = '<span class="info">🔄 Testing Gemini API...</span>';
            
            try {
                // Test Gemini API directly
                const apiKey = 'AIzaSyBDog2FNMNdvcebrk_WteAL_9WwaQHgMqg';
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: "Say hello in exactly 10 words"
                            }]
                        }]
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const text = data.candidates?.[0]?.content?.parts?.[0]?.text || 'No response';
                
                output.innerHTML = `<span class="success">✅ Gemini API works!</span><br><strong>Response:</strong> ${text}`;
            } catch (error) {
                output.innerHTML = `<span class="error">❌ Gemini API Error: ${error.message}</span>`;
            }
        };
        
        window.testOpenRouterAPI = async () => {
            const output = document.getElementById('output');
            output.innerHTML = '<span class="info">🔄 Testing OpenRouter API...</span>';
            
            try {
                // Test OpenRouter API directly
                const apiKey = '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';
                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': 'http://localhost:5174',
                        'X-Title': 'Assessment Streaming Test'
                    },
                    body: JSON.stringify({
                        model: 'meta-llama/llama-3.2-3b-instruct:free',
                        messages: [{
                            role: 'user',
                            content: 'Say hello in exactly 10 words'
                        }],
                        max_tokens: 50
                    })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const data = await response.json();
                const text = data.choices?.[0]?.message?.content || 'No response';
                
                output.innerHTML = `<span class="success">✅ OpenRouter API works!</span><br><strong>Response:</strong> ${text}`;
            } catch (error) {
                output.innerHTML = `<span class="error">❌ OpenRouter API Error: ${error.message}</span>`;
            }
        };
    </script>
</body>
</html>
