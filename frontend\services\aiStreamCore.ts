// services/aiStreamCore.ts
// This file now only contains type definitions for the unified streaming system

/**
 * Unified Streaming Types
 */
export type AIStreamChunk = {
  text: string;                  // Current text delta
  isComplete: boolean;           // True if this is the final chunk
  metadata?: {                   // Provider-specific metadata
    groundingChunks?: any[];     // Gemini web results
    usage?: {                   // Token usage (OpenAI-style)
      promptTokens?: number;
      completionTokens?: number;
    };
    // Additional provider fields can be added here
  };
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
};

export interface AIProviderConfig {
  apiKey: string;
  defaultModel?: string;
  // Additional provider-specific config
}

export interface AIMessageRequest {
  prompt: string;
  systemMessage?: string;
  model?: string;
  temperature?: number;
  // Unified search parameters
  search?: {
    web?: boolean;
    // Future: custom search backends
  };
  // Conversation context for stateful chats
  conversationId?: string;
}
