import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { CloseIcon, TrashIcon } from "../constants";

interface DeleteAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  userName: string;
}

const DeleteAccountModal: React.FC<DeleteAccountModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  userName,
}) => {
  const { t } = useTranslation();
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmText, setConfirmText] = useState("");
  const [error, setError] = useState<string | null>(null);

  const expectedText = "DELETE";

  if (!isOpen) return null;

  const handleConfirm = async () => {
    if (confirmText !== expectedText) {
      setError(t("confirmTextMismatch", 'Please type "DELETE" to confirm'));
      return;
    }

    setIsDeleting(true);
    setError(null);

    try {
      await onConfirm();
      // Modal will be closed by parent component after successful deletion
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : t("deleteAccountError", "Failed to delete account"),
      );
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      setConfirmText("");
      setError(null);
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      data-oid="clsj921"
    >
      <div
        className="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-md w-full mx-4"
        data-oid="5:h08ey"
      >
        <div
          className="flex justify-between items-center mb-6"
          data-oid="i639htr"
        >
          <div className="flex items-center" data-oid="oiszb3n">
            <TrashIcon
              className="w-6 h-6 text-red-600 mr-3"
              data-oid="8_ww_-p"
            />

            <h3
              className="text-xl font-semibold text-slate-900 dark:text-slate-100"
              data-oid="stholxz"
            >
              {t("deleteAccount", "Delete Account")}
            </h3>
          </div>
          {!isDeleting && (
            <button
              onClick={handleClose}
              className="text-slate-400 hover:text-slate-500"
              data-oid="30cvk2u"
            >
              <CloseIcon className="w-6 h-6" data-oid="wtcoqxu" />
            </button>
          )}
        </div>

        <div className="space-y-4" data-oid="i.2nggq">
          <div
            className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4"
            data-oid="9vfqdf9"
          >
            <h4
              className="font-medium text-red-900 dark:text-red-100 mb-2"
              data-oid="tsnsqy8"
            >
              {t("warningTitle", "Warning: This action cannot be undone")}
            </h4>
            <ul
              className="text-sm text-red-800 dark:text-red-200 space-y-1"
              data-oid="60qb8xc"
            >
              <li data-oid="cvcry9p">
                •{" "}
                {t(
                  "deleteAccountWarning1",
                  "Your account will be permanently deleted",
                )}
              </li>
              <li data-oid="ontc_k4">
                •{" "}
                {t(
                  "deleteAccountWarning2",
                  "All your chat history will be lost",
                )}
              </li>
              <li data-oid="yc2rl1s">
                •{" "}
                {t(
                  "deleteAccountWarning3",
                  "You will be immediately signed out",
                )}
              </li>
              <li data-oid="va14bh.">
                • {t("deleteAccountWarning4", "This action cannot be reversed")}
              </li>
            </ul>
          </div>

          <div data-oid="0_hc8mp">
            <p
              className="text-sm text-slate-600 dark:text-slate-400 mb-2"
              data-oid="uwlppe5"
            >
              {t(
                "deleteAccountConfirmText",
                'To confirm deletion of your account ({userName}), please type "DELETE" below:',
                { userName },
              )}
            </p>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder="DELETE"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md focus:ring-red-500 focus:border-red-500 dark:bg-slate-700 dark:text-slate-100"
              disabled={isDeleting}
              data-oid="kfxj323"
            />
          </div>

          {error && (
            <div
              className="p-3 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md text-sm"
              data-oid="_x-jshr"
            >
              {error}
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3 mt-6" data-oid="ir:k27s">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 rounded-lg transition-colors"
            disabled={isDeleting}
            data-oid="pdrdvta"
          >
            {t("cancel")}
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-lg transition-colors flex items-center"
            disabled={isDeleting || confirmText !== expectedText}
            data-oid="ta8lc1_"
          >
            {isDeleting ? (
              <>
                <div
                  className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
                  data-oid="yh_zp2n"
                ></div>
                {t("deleting", "Deleting...")}
              </>
            ) : (
              <>
                <TrashIcon className="w-4 h-4 mr-2" data-oid="3whyo27" />
                {t("deleteAccountPermanently", "Delete Account Permanently")}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteAccountModal;
