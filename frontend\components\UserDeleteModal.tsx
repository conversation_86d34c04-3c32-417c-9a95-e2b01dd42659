import React from "react";
import { useTranslation } from "react-i18next";
import { CloseIcon } from "../constants";

interface UserData {
  id: string;
  name: string;
  email: string;
  role: "admin" | "user";
  avatarUrl?: string;
  createdAt: Date;
  lastActive: Date;
}

interface UserDeleteModalProps {
  user: UserData;
  onConfirm: () => void;
  onCancel: () => void;
}

const UserDeleteModal: React.FC<UserDeleteModalProps> = ({
  user,
  onConfirm,
  onCancel,
}) => {
  const { t } = useTranslation();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100">
            {t("deleteUser")}
          </h3>
          <button
            onClick={onCancel}
            className="text-slate-400 hover:text-slate-500 dark:text-slate-500 dark:hover:text-slate-400"
          >
            <CloseIcon className="w-5 h-5" />
          </button>
        </div>

        <div className="mb-6">
          <p className="text-slate-600 dark:text-slate-300 mb-2">
            {t("confirmDelete")}
          </p>
          <div className="bg-slate-50 dark:bg-slate-700 p-3 rounded-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0 h-10 w-10">
                {user.avatarUrl ? (
                  <img
                    className="h-10 w-10 rounded-full"
                    src={user.avatarUrl}
                    alt={user.name}
                  />
                ) : (
                  <div className="h-10 w-10 rounded-full bg-slate-300 dark:bg-slate-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      {user.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-slate-900 dark:text-slate-100">
                  {user.name}
                </div>
                <div className="text-sm text-slate-500 dark:text-slate-400">
                  {user.email}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 rounded-lg transition-colors"
          >
            {t("cancel", "Cancel")}
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            {t("delete", "Delete")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserDeleteModal;
