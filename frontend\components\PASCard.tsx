import React, { useRef, useCallback } from "react";
import { useSwipeGesture } from "../hooks/useSwipeGesture";

interface PASCardProps {
  id: string;
  title: string;
  description: string;
  icon?: string;
  onSelect: (id: string) => void;
  // Swipe-related props
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  isSwipeable?: boolean;
  // Visual state for swiping
  transform?: string;
  transition?: string;
  className?: string;
}

export const PASCard: React.FC<PASCardProps> = ({
  id,
  title,
  description,
  icon,
  onSelect,
  onSwipeLeft,
  onSwipeRight,
  isSwipeable = false,
  transform = "",
  transition = "",
  className = "",
}) => {
  const cardRef = useRef<HTMLButtonElement>(null);
  const hasSwipedRef = useRef(false);

  // Handle swipe gestures
  const swipeHandlers = useSwipeGesture({
    onSwipeLeft: onSwipeLeft,
    onSwipeRight: onSwipeRight,
    onSwipeStart: () => {
      hasSwipedRef.current = false;
    },
    onSwipeMove: (deltaX) => {
      // Visual feedback during swipe
      if (cardRef.current && isSwipeable) {
        const clampedDelta = Math.max(-100, Math.min(100, deltaX * 0.3));
        cardRef.current.style.transform = `translateX(${clampedDelta}px)`;
        cardRef.current.style.transition = "none";
      }
    },
    onSwipeEnd: (deltaX, deltaY, velocity) => {
      // Reset visual state
      if (cardRef.current && isSwipeable) {
        cardRef.current.style.transform = transform;
        cardRef.current.style.transition =
          transition || "transform 0.3s ease-out";
      }

      // Mark as swiped if it was a valid swipe
      const absX = Math.abs(deltaX);
      const absY = Math.abs(deltaY);
      if (absX > absY && absX > 50 && velocity > 0.3) {
        hasSwipedRef.current = true;
      }
    },
    threshold: 50,
    velocityThreshold: 0.3,
    preventDefaultTouch: isSwipeable,
  });

  // Handle click - only trigger if not swiped
  const handleClick = useCallback(() => {
    console.log("🎯 [PASCard] Card clicked:", { id, title });
    // Small delay to allow swipe detection to complete
    setTimeout(() => {
      if (!hasSwipedRef.current) {
        console.log("✅ [PASCard] Calling onSelect with id:", id);
        onSelect(id);
      } else {
        console.log("⚠️ [PASCard] Click ignored due to swipe");
      }
      hasSwipedRef.current = false;
    }, 10);
  }, [id, title, onSelect]);

  const baseClassName =
    "flex flex-col items-center justify-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-md p-4 m-2 w-64 h-40 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-shadow select-none";

  const combinedClassName = className
    ? `${baseClassName} ${className}`
    : baseClassName;

  const touchHandlers = isSwipeable
    ? {
        onTouchStart: swipeHandlers.onTouchStart,
        onTouchMove: swipeHandlers.onTouchMove,
        onTouchEnd: swipeHandlers.onTouchEnd,
        // Mouse handlers for desktop testing
        onMouseDown: swipeHandlers.onMouseDown,
        onMouseMove: swipeHandlers.onMouseMove,
        onMouseUp: swipeHandlers.onMouseUp,
      }
    : {};

  return (
    <button
      ref={cardRef}
      className={combinedClassName}
      onClick={handleClick}
      aria-label={title}
      type="button"
      style={{
        transform: transform,
        transition: transition,
        touchAction: isSwipeable ? "pan-y" : "auto", // Allow vertical scrolling but handle horizontal
      }}
      {...touchHandlers}
      data-oid="5u_k61u"
    >
      {icon && (
        <img src={icon} alt="" className="w-8 h-8 mb-3" data-oid="uux9l3d" />
      )}
      <span
        className="font-semibold text-sm mb-1 text-center leading-tight"
        data-oid="3a50ytb"
      >
        {title}
      </span>
      <span
        className="text-xs text-gray-500 dark:text-gray-400 text-center leading-tight"
        data-oid="wt2dnby"
      >
        {description}
      </span>
    </button>
  );
};
