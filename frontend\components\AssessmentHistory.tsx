import React, { useState, useEffect } from "react";
import { assessmentService } from "../services/assessmentService";
import { AssessmentResultResponse } from "../types/assessment";

interface AssessmentHistoryProps {
  sessionId?: string;
  userId?: string;
  onViewResult?: (result: AssessmentResultResponse) => void;
}

export const AssessmentHistory: React.FC<AssessmentHistoryProps> = ({
  sessionId,
  userId,
  onViewResult,
}) => {
  const [results, setResults] = useState<AssessmentResultResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAssessmentHistory = async () => {
      if (!sessionId) return;

      try {
        setIsLoading(true);
        setError(null);
        const assessmentResults =
          await assessmentService.getAssessmentResults(sessionId);
        setResults(assessmentResults);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Failed to load assessment history";
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadAssessmentHistory();
  }, [sessionId]);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  const getAssessmentTypeLabel = (type: string) => {
    switch (type) {
      case "6HN":
        return "6 Human Needs";
      case "MBTI":
        return "Myers-Briggs Type Indicator";
      case "DiSC":
        return "DiSC Assessment";
      default:
        return type;
    }
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 90) return "text-green-600 dark:text-green-400";
    if (percentage >= 70) return "text-blue-600 dark:text-blue-400";
    if (percentage >= 50) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8" data-oid="bwpbpeo">
        <div className="text-center" data-oid="5l_f-86">
          <svg
            className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4"
            fill="none"
            viewBox="0 0 24 24"
            data-oid="0ktn5sv"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
              data-oid="w:1ii:j"
            />

            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              data-oid="-znsed_"
            />
          </svg>
          <p className="text-gray-600 dark:text-gray-400" data-oid=".1zuo5w">
            Loading assessment history...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        data-oid="jy3ep7y"
      >
        <div className="flex items-center space-x-3 mb-4" data-oid="gewy3:z">
          <svg
            className="w-6 h-6 text-red-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            data-oid="vqbmqt4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              data-oid="xh5wxcq"
            />
          </svg>
          <h3
            className="text-lg font-medium text-red-800 dark:text-red-200"
            data-oid="e9q46cs"
          >
            Error Loading History
          </h3>
        </div>
        <p className="text-red-700 dark:text-red-300" data-oid="yfv48ry">
          {error}
        </p>
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className="text-center p-8" data-oid="jizl8-n">
        <div
          className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4"
          data-oid="cr95z3a"
        >
          <svg
            className="w-8 h-8 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            data-oid="0t.zwqi"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              data-oid=":g-p_28"
            />
          </svg>
        </div>
        <h3
          className="text-lg font-medium text-gray-900 dark:text-white mb-2"
          data-oid="s5caa_5"
        >
          No Assessments Yet
        </h3>
        <p className="text-gray-600 dark:text-gray-400" data-oid="8_:j803">
          You haven't completed any personality assessments yet. Take your first
          assessment to get started!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4" data-oid="l277y._">
      <h3
        className="text-lg font-semibold text-gray-900 dark:text-white mb-4"
        data-oid="84j584v"
      >
        Assessment History ({results.length})
      </h3>

      {results.map((resultResponse) => {
        const { result, insights } = resultResponse;
        const topInsights = insights.slice(0, 2);

        return (
          <div
            key={result.id}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow"
            data-oid="hakpnd4"
          >
            <div
              className="flex items-start justify-between mb-4"
              data-oid="jc4u5n_"
            >
              <div className="flex-1" data-oid="j.quad1">
                <h4
                  className="text-lg font-medium text-gray-900 dark:text-white mb-1"
                  data-oid="74ih0r3"
                >
                  {getAssessmentTypeLabel(result.result_json.type)}
                </h4>
                <p
                  className="text-sm text-gray-600 dark:text-gray-400"
                  data-oid="h45mqny"
                >
                  Completed on {formatDate(result.created_at)}
                </p>
              </div>

              <div className="text-right" data-oid="q2esmrj">
                <div
                  className={`text-sm font-medium ${getCompletionColor(result.result_json.completion_percentage)}`}
                  data-oid="v0qzp50"
                >
                  {result.result_json.completion_percentage.toFixed(0)}%
                  Complete
                </div>
                {onViewResult && (
                  <button
                    onClick={() => onViewResult(resultResponse)}
                    className="mt-2 px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-md transition-colors"
                    data-oid="byg0w6o"
                  >
                    View Details
                  </button>
                )}
              </div>
            </div>

            {/* Dominant Traits */}
            {result.result_json.dominant_traits.length > 0 && (
              <div className="mb-4" data-oid="q1k0x27">
                <p
                  className="text-sm text-gray-600 dark:text-gray-400 mb-2"
                  data-oid="x5ys1ww"
                >
                  Dominant Traits:
                </p>
                <div className="flex flex-wrap gap-2" data-oid="ltu65ia">
                  {result.result_json.dominant_traits.map((trait, index) => (
                    <span
                      key={trait}
                      className={`px-2 py-1 text-xs font-medium rounded-full ${
                        index === 0
                          ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                          : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                      }`}
                      data-oid="5mttukv"
                    >
                      {trait}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Top Insights Preview */}
            {topInsights.length > 0 && (
              <div data-oid="gvi6.kg">
                <p
                  className="text-sm text-gray-600 dark:text-gray-400 mb-2"
                  data-oid="xxzqeod"
                >
                  Key Insights:
                </p>
                <div className="space-y-2" data-oid="xvwgect">
                  {topInsights.map((insight, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between text-sm"
                      data-oid="z:4nbl7"
                    >
                      <span
                        className="text-gray-700 dark:text-gray-300"
                        data-oid="qx-42po"
                      >
                        {insight.trait}
                      </span>
                      <span
                        className="font-medium text-gray-900 dark:text-white"
                        data-oid="y9g.sv0"
                      >
                        {insight.score.toFixed(1)}/10
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
