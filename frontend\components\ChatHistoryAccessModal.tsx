import React from "react";
import { useTranslation } from "react-i18next";
import { CloseIcon } from "../constants";

interface ChatHistoryAccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSignInRedirect: () => void;
}

const ChatHistoryAccessModal: React.FC<ChatHistoryAccessModalProps> = ({
  isOpen,
  onClose,
  onSignInRedirect,
}) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-60 dark:bg-black/70 z-[70] flex items-center justify-center p-4 transition-opacity duration-300 ease-in-out"
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
      role="dialog"
      aria-modal="true"
      aria-labelledby="chat-history-access-title"
      data-oid="mt:93n:"
    >
      <div
        className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-sm p-6 sm:p-8 relative text-center"
        data-oid="eyrlzjc"
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-1.5 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full"
          aria-label={t("closeSettings")}
          data-oid="1_5857_"
        >
          <CloseIcon className="w-5 h-5" data-oid="i4-ju05" />
        </button>

        <h1
          id="chat-history-access-title"
          className="text-2xl font-semibold text-[#2A2B31] dark:text-slate-100 mb-3"
          data-oid="blv:w9k"
        >
          {t("chatHistoryAccessTitle")}
        </h1>
        <p
          className="text-sm text-[#787878] dark:text-slate-400 mb-8"
          data-oid="8zwzy8d"
        >
          {t("chatHistoryAccessDescription")}
        </p>

        <div className="space-y-3" data-oid="sui:oes">
          <button
            onClick={onSignInRedirect}
            className="w-full bg-[#222428] dark:bg-slate-700 hover:bg-opacity-90 dark:hover:bg-slate-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            data-oid="rwp-ogw"
          >
            {t("signIn")}
          </button>
          <button
            onClick={onClose}
            className="w-full bg-white dark:bg-slate-600 hover:bg-slate-50 dark:hover:bg-slate-500 text-[#4B4B4B] dark:text-slate-200 font-medium py-3 px-4 rounded-lg border border-slate-300 dark:border-slate-500 transition-colors"
            data-oid="cg5g4._"
          >
            {t("stayLoggedOut")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatHistoryAccessModal;
