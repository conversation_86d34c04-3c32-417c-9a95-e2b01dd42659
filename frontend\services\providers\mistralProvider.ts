// services/providers/mistralProvider.ts
import { AIProviderConfig } from '../aiStreamCore';
import { AIProvider, UnifiedAIResponse } from '../unifiedStreamingService';

/**
 * Mistral Provider Implementation
 */
export class MistralProvider implements AIProvider {
  private apiKey: string;
  private baseUrl: string;
  private defaultModel: string;

  constructor(config: AIProviderConfig) {
    this.apiKey = config.apiKey;
    this.defaultModel = config.defaultModel || 'mistral-large-latest';
    this.baseUrl = 'https://api.mistral.ai/v1';
  }

  async sendMessage(request: any): Promise<UnifiedAIResponse> {
    try {
      // Prepare messages array with system message if present
      const messages = [];
      if (request.systemMessage) {
        messages.push({ role: 'system', content: request.systemMessage });
      }
      messages.push({ role: 'user', content: request.prompt });

      // Mistral API request
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          model: request.model || this.defaultModel,
          messages: messages,
          stream: false, // Get complete response
          temperature: request.temperature || 0.7,
          max_tokens: 4096
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Mistral API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`
        );
      }

      const data = await response.json();
      
      if (data.choices && data.choices[0]?.message?.content) {
        return {
          text: data.choices[0].message.content,
          metadata: {
            usage: data.usage
          }
        };
      } else {
        throw new Error('Invalid response format from Mistral API');
      }

    } catch (error) {
      return {
        text: '',
        error: {
          code: 'mistral_error',
          message: error instanceof Error ? error.message : 'Unknown Mistral error',
          retryable: true
        }
      };
    }
  }

  supportsFeature(feature: string): boolean {
    // Mistral doesn't support web search in the same way as Gemini
    return false;
  }
} 